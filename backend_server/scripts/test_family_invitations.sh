#!/bin/bash

# Test script for family group invitation functionality

# Default environment variables
API_BASE="http://localhost:5678"
ACCESS_TOKEN=""
USER_ID="022b442c1f021000"
USER_ID2="022b442c1f021001"
GROUP_ID="fg_028a5f4e85821000"
INVITATION_ID=""

# Debug flag (set to 1 to enable detailed debug output)
DEBUG=1

# Terminal colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    
    case $level in
        "INFO") echo -e "${BLUE}[INFO]${NC} $timestamp - $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $timestamp - $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $timestamp - $message" ;;
        "DEBUG") 
            if [ "$DEBUG" = "1" ]; then
                echo -e "${YELLOW}[DEBUG]${NC} $timestamp - $message"
            fi
            ;;
        *) echo -e "$timestamp - $message" ;;
    esac
}

# Function to check if jq is installed
check_dependencies() {
    if ! command -v jq &> /dev/null; then
        log "ERROR" "jq is required but not installed. Please install jq first."
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log "ERROR" "curl is required but not installed. Please install curl first."
        exit 1
    fi
}

# Function to clean up response data for better parsing
cleanup_response() {
    local response="$1"
    echo "$response" | sed 's/\x1B\[[0-9;]*[JKmsu]//g' | tr -dc '[:print:]\n'
}

# Function to extract clean JSON from response
extract_json() {
    local response="$1"
    local cleaned_response=$(cleanup_response "$response")
    
    local json_pattern=$(echo "$cleaned_response" | grep -o '{.*}')
    if [ -n "$json_pattern" ] && echo "$json_pattern" | jq . &> /dev/null; then
        echo "$json_pattern"
        return 0
    fi
    
    if [[ "$cleaned_response" =~ (\{.*\}) ]]; then
        local json_match="${BASH_REMATCH[1]}"
        if echo "$json_match" | jq . &> /dev/null; then
            echo "$json_match"
            return 0
        fi
    fi
    
    log "ERROR" "Could not extract valid JSON from response"
    log "DEBUG" "Full cleaned response: $cleaned_response"
    return 1
}

# Function to handle API requests
make_request() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=${4:-""}
    local additional_headers=${5:-""}
    
    local auth_header=""
    if [ -n "$ACCESS_TOKEN" ]; then
        auth_header="-H \"Authorization: Bearer $ACCESS_TOKEN\""
    fi
    
    log "INFO" "Making $method request to $endpoint - $description"
    
    local cmd="curl -s -X $method"
    
    if [ -n "$auth_header" ]; then
        cmd="$cmd $auth_header"
    fi
    
    if [ -n "$additional_headers" ]; then
        cmd="$cmd $additional_headers"
    fi
    
    cmd="$cmd -H \"Content-Type: application/json\""
    
    if [ -n "$data" ]; then
        cmd="$cmd -d '$data'"
    fi
    
    cmd="$cmd \"${API_BASE}${endpoint}\""
    
    if [ "$DEBUG" = "1" ]; then
        log "DEBUG" "Executing command: $cmd"
    fi
    
    local raw_response=$(eval $cmd)
    
    local clean_response=""
    if [[ "$raw_response" =~ (\{.*\}) ]]; then
        clean_response="${BASH_REMATCH[1]}"
    else
        local json_start=$(echo "$raw_response" | grep -b -o '{' | head -n 1 | cut -d: -f1)
        if [ -n "$json_start" ]; then
            clean_response=$(echo "$raw_response" | tail -c +$((json_start+1)))
        else
            clean_response="$raw_response"
        fi
    fi
    
    if echo "$clean_response" | jq . &> /dev/null; then
        log "SUCCESS" "Response received:"
        echo "$clean_response" | jq .
        
        local error_msg=$(echo "$clean_response" | jq -r '.error')
        if [ -n "$error_msg" ] && [ "$error_msg" != "null" ]; then
            log "ERROR" "API error: $error_msg"
        fi
        
        if [ "$DEBUG" = "1" ]; then
            log "DEBUG" "Response keys: $(echo "$clean_response" | jq 'keys')"
        fi
        
        raw_response="$clean_response"
    else
        log "ERROR" "Invalid JSON response received:"
        echo "$raw_response"
    fi
    
    echo "$raw_response"
}

# Function to get future date (compatible with both GNU and BSD date)
get_future_date() {
    local hours=$1
    
    # Try GNU date first (Linux)
    if date -u -d "+${hours} hours" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        return 0
    fi
    
    # Try BSD date (macOS)
    if date -u -v+${hours}H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        return 0
    fi
    
    # Fallback - use current time + rough calculation
    echo "2025-12-31T23:59:59Z"
}

# Test 1: Create family group invitation by user ID
test_create_invitation_by_user_id() {
    log "INFO" "Testing: Create family group invitation by user ID"
    
    if [ -z "$GROUP_ID" ]; then
        log "ERROR" "GROUP_ID is not set. Please set it or create a family group first."
        return
    fi
    
    read -p "Enter invitee user ID (default: $USER_ID2): " invitee_id
    invitee_id=${invitee_id:-$USER_ID2}
    
    read -p "Enter role (1=Owner, 2=Admin, 3=Normal, default: 3): " role
    role=${role:-3}
    
    read -p "Enter expiration time (ISO8601 format, default: 24h from now): " expire_time
    if [ -z "$expire_time" ]; then
        expire_time=$(get_future_date 24)
    fi
    
    local data="{\"invitee_id\":\"$invitee_id\",\"role\":$role,\"expire_at\":\"$expire_time\"}"
    
    local response=$(make_request "POST" "/api/family-groups/$GROUP_ID/invitations?user_id=$USER_ID" "Create invitation by user ID" "$data")
    
    INVITATION_ID=$(echo "$response" | jq -r '.data.invitation_id // empty')
    if [ -n "$INVITATION_ID" ] && [ "$INVITATION_ID" != "null" ]; then
        log "SUCCESS" "Created invitation with ID: $INVITATION_ID"
    fi
}

# Test 2: Create family group invitation by email
test_create_invitation_by_email() {
    log "INFO" "Testing: Create family group invitation by email"
    
    if [ -z "$GROUP_ID" ]; then
        log "ERROR" "GROUP_ID is not set. Please set it or create a family group first."
        return
    fi
    
    read -p "Enter invitee email: " invitee_email
    if [ -z "$invitee_email" ]; then
        log "ERROR" "Email is required."
        return
    fi
    
    read -p "Enter role (1=Owner, 2=Admin, 3=Normal, default: 3): " role
    role=${role:-3}
    
    read -p "Enter expiration time (ISO8601 format, default: 24h from now): " expire_time
    if [ -z "$expire_time" ]; then
        expire_time=$(get_future_date 24)
    fi
    
    local data="{\"invitee_email\":\"$invitee_email\",\"role\":$role,\"expire_at\":\"$expire_time\"}"
    
    local response=$(make_request "POST" "/api/family-groups/$GROUP_ID/invitations/email?user_id=$USER_ID" "Create invitation by email" "$data")
    
    local new_invitation_id=$(echo "$response" | jq -r '.data.invitation_id // empty')
    if [ -n "$new_invitation_id" ] && [ "$new_invitation_id" != "null" ]; then
        log "SUCCESS" "Created invitation with ID: $new_invitation_id"
        if [ -z "$INVITATION_ID" ]; then
            INVITATION_ID="$new_invitation_id"
        fi
    fi
}

# Test 3: Get family group invitation details
test_get_invitation_details() {
    log "INFO" "Testing: Get family group invitation details"
    
    local target_invitation_id="$INVITATION_ID"
    read -p "Enter invitation ID (default: $INVITATION_ID): " input_id
    if [ -n "$input_id" ]; then
        target_invitation_id="$input_id"
    fi
    
    if [ -z "$target_invitation_id" ]; then
        log "ERROR" "Invitation ID is required."
        return
    fi
    
    make_request "GET" "/api/family-groups/invitations/$target_invitation_id?user_id=$USER_ID" "Get invitation details"
}

# Test 4: List received invitations
test_list_received_invitations() {
    log "INFO" "Testing: List received family group invitations"
    
    read -p "Enter user ID to check received invitations (default: $USER_ID2): " target_user
    target_user=${target_user:-$USER_ID2}
    
    make_request "GET" "/api/family-groups/invitations/received?user_id=$target_user" "List received invitations"
}

# Test 5: List sent invitations
test_list_sent_invitations() {
    log "INFO" "Testing: List sent family group invitations"
    
    read -p "Enter user ID to check sent invitations (default: $USER_ID): " target_user
    target_user=${target_user:-$USER_ID}
    
    make_request "GET" "/api/family-groups/invitations/sent?user_id=$target_user" "List sent invitations"
}

# Test 6: Process invitation (accept)
test_accept_invitation() {
    log "INFO" "Testing: Accept family group invitation"
    
    local target_invitation_id="$INVITATION_ID"
    read -p "Enter invitation ID to accept (default: $INVITATION_ID): " input_id
    if [ -n "$input_id" ]; then
        target_invitation_id="$input_id"
    fi
    
    if [ -z "$target_invitation_id" ]; then
        log "ERROR" "Invitation ID is required."
        return
    fi
    
    read -p "Enter user ID who will accept (default: $USER_ID2): " accepting_user
    accepting_user=${accepting_user:-$USER_ID2}
    
    make_request "PUT" "/api/family-groups/invitations/$target_invitation_id/process?user_id=$accepting_user&action=accept" "Accept invitation"
}

# Test 7: Process invitation (reject)
test_reject_invitation() {
    log "INFO" "Testing: Reject family group invitation"
    
    local target_invitation_id="$INVITATION_ID"
    read -p "Enter invitation ID to reject (default: $INVITATION_ID): " input_id
    if [ -n "$input_id" ]; then
        target_invitation_id="$input_id"
    fi
    
    if [ -z "$target_invitation_id" ]; then
        log "ERROR" "Invitation ID is required."
        return
    fi
    
    read -p "Enter user ID who will reject (default: $USER_ID2): " rejecting_user
    rejecting_user=${rejecting_user:-$USER_ID2}
    
    make_request "PUT" "/api/family-groups/invitations/$target_invitation_id/process?user_id=$rejecting_user&action=reject" "Reject invitation"
}

# Test 8: Cancel family group invitation
test_cancel_invitation() {
    log "INFO" "Testing: Cancel family group invitation"
    
    local target_invitation_id="$INVITATION_ID"
    read -p "Enter invitation ID to cancel (default: $INVITATION_ID): " input_id
    if [ -n "$input_id" ]; then
        target_invitation_id="$input_id"
    fi
    
    if [ -z "$target_invitation_id" ]; then
        log "ERROR" "Invitation ID is required."
        return
    fi
    
    read -p "Enter user ID who will cancel (default: $USER_ID): " canceling_user
    canceling_user=${canceling_user:-$USER_ID}
    
    make_request "DELETE" "/api/family-groups/invitations/$target_invitation_id/cancel?user_id=$canceling_user" "Cancel invitation"
}

# Test 9: Error cases
test_error_cases() {
    log "INFO" "Testing: Error cases"
    
    echo ""
    echo "Select error case to test:"
    echo "1. Invalid email invitation"
    echo "2. Unauthorized invitation creation"
    echo "3. Invalid invitation processing"
    echo "4. Process already processed invitation"
    echo "5. Cancel non-existent invitation"
    
    read -p "Select error case (1-5): " error_case
    
    case $error_case in
        1)
            log "INFO" "Testing invalid email invitation..."
            local data='{"invitee_email":"<EMAIL>","role":3}'
            make_request "POST" "/api/family-groups/$GROUP_ID/invitations/email?user_id=$USER_ID" "Invalid email invitation" "$data"
            ;;
        2)
            log "INFO" "Testing unauthorized invitation creation..."
            local data='{"invitee_id":"'$USER_ID2'","role":3}'
            make_request "POST" "/api/family-groups/$GROUP_ID/invitations?user_id=unauthorized_user" "Unauthorized invitation creation" "$data"
            ;;
        3)
            log "INFO" "Testing invalid invitation processing..."
            make_request "PUT" "/api/family-groups/invitations/invalid_invitation_id/process?user_id=$USER_ID2&action=accept" "Invalid invitation processing"
            ;;
        4)
            log "INFO" "Testing processing already processed invitation..."
            if [ -n "$INVITATION_ID" ]; then
                make_request "PUT" "/api/family-groups/invitations/$INVITATION_ID/process?user_id=$USER_ID2&action=accept" "Process already processed invitation"
            else
                log "ERROR" "No invitation ID available for testing."
            fi
            ;;
        5)
            log "INFO" "Testing cancel non-existent invitation..."
            make_request "DELETE" "/api/family-groups/invitations/non_existent_id/cancel?user_id=$USER_ID" "Cancel non-existent invitation"
            ;;
        *)
            log "ERROR" "Invalid error case selection."
            ;;
    esac
}

# Test 10: Set configuration values
test_set_config() {
    log "INFO" "Setting configuration values..."
    
    echo "Current values:"
    echo "  API_BASE: $API_BASE"
    echo "  USER_ID: $USER_ID"
    echo "  USER_ID2: $USER_ID2"
    echo "  GROUP_ID: $GROUP_ID"
    echo "  INVITATION_ID: $INVITATION_ID"
    echo ""
    
    read -p "Enter new API_BASE (press enter to keep current): " new_api_base
    if [ -n "$new_api_base" ]; then
        API_BASE="$new_api_base"
    fi
    
    read -p "Enter new USER_ID (press enter to keep current): " new_user_id
    if [ -n "$new_user_id" ]; then
        USER_ID="$new_user_id"
    fi
    
    read -p "Enter new USER_ID2 (press enter to keep current): " new_user_id2
    if [ -n "$new_user_id2" ]; then
        USER_ID2="$new_user_id2"
    fi
    
    read -p "Enter new GROUP_ID (press enter to keep current): " new_group_id
    if [ -n "$new_group_id" ]; then
        GROUP_ID="$new_group_id"
    fi
    
    read -p "Enter new INVITATION_ID (press enter to keep current): " new_invitation_id
    if [ -n "$new_invitation_id" ]; then
        INVITATION_ID="$new_invitation_id"
    fi
    
    log "SUCCESS" "Configuration updated."
}

# Main function
main() {
    check_dependencies
    
    # Parse command line arguments
    while getopts ":a:t:u:g:h" opt; do
        case ${opt} in
            a )
                API_BASE=$OPTARG
                ;;
            t )
                ACCESS_TOKEN=$OPTARG
                ;;
            u )
                USER_ID=$OPTARG
                ;;
            g )
                GROUP_ID=$OPTARG
                ;;
            h )
                echo "Usage: $0 [-a API_BASE] [-t ACCESS_TOKEN] [-u USER_ID] [-g GROUP_ID]"
                exit 0
                ;;
            \? )
                echo "Invalid option: $OPTARG" 1>&2
                exit 1
                ;;
            : )
                echo "Invalid option: $OPTARG requires an argument" 1>&2
                exit 1
                ;;
        esac
    done
    
    log "INFO" "Starting family group invitation API test script..."
    log "INFO" "API base URL: $API_BASE"
    log "INFO" "Using USER_ID: $USER_ID"
    log "INFO" "Using USER_ID2: $USER_ID2"
    log "INFO" "Using GROUP_ID: $GROUP_ID"
    
    # Show test menu
    while true; do
        echo ""
        echo "==== Family Group Invitation Test Menu ===="
        echo "1.  Create invitation by user ID"
        echo "2.  Create invitation by email"
        echo "3.  Get invitation details"
        echo "4.  List received invitations"
        echo "5.  List sent invitations"
        echo "6.  Accept invitation"
        echo "7.  Reject invitation"
        echo "8.  Cancel invitation"
        echo "9.  Test error cases"
        echo "10. Set configuration values"
        echo "0.  Exit"
        echo "============================================"
        
        read -p "Select an option: " option
        echo ""
        
        case $option in
            1) test_create_invitation_by_user_id ;;
            2) test_create_invitation_by_email ;;
            3) test_get_invitation_details ;;
            4) test_list_received_invitations ;;
            5) test_list_sent_invitations ;;
            6) test_accept_invitation ;;
            7) test_reject_invitation ;;
            8) test_cancel_invitation ;;
            9) test_error_cases ;;
            10) test_set_config ;;
            0) exit 0 ;;
            *) log "ERROR" "Invalid option." ;;
        esac
    done
}

# Run main function
main "$@"