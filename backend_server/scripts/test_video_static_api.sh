#!/bin/bash

# Script to test the backend_server video static API endpoint

# --- Configuration ---
API_URL="http://***************:5678/api"
API_HEALTH_PATH="/health"
API_VIDEO_STATIC_PATH="/videos/static"
SERVICE_TOKEN="03U66tGbSQtHGrh9IyBDjRYaSeQukFga"

# Sample video ID (adjust as needed)
VIDEO_ID="93c2531d58f3ce1b67be7161"
USER_ID="022b605dc3421000"

# --- Colors ---
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# --- Variables for process tracking ---
CURL_PID=""

# --- Trap handler for SIGINT ---
trap ctrl_c INT

ctrl_c() {
    echo -e "\n${YELLOW}Received Ctrl+C. Canceling request...${NC}"
    if [ -n "$CURL_PID" ]; then
        kill -s TERM $CURL_PID 2>/dev/null || true
        echo -e "${YELLOW}Request canceled.${NC}"
    fi
    exit 130
}

# --- Helper Function ---
log_step() {
    echo -e "\n${YELLOW}>>> $1${NC}"
}

check_result() {
    local http_code=$1
    local response_body=$2
    local expected_code=$3
    local test_name=$4

    echo "Response Code: $http_code"
    echo "Response Body: $response_body" | jq . 2>/dev/null || echo "Response Body: $response_body"

    if [ "$http_code" -eq "$expected_code" ]; then
        echo -e "${GREEN}✓ Test PASSED: $test_name (Expected: $expected_code, Got: $http_code)${NC}"
        return 0
    else
        echo -e "${RED}✗ Test FAILED: $test_name (Expected: $expected_code, Got: $http_code)${NC}"
        return 1
    fi
}

# --- Pre-checks ---
log_step "Checking prerequisites (curl, jq)"
command -v jq >/dev/null 2>&1 || { echo -e "${RED}Error: jq is not installed. Please install jq.${NC}"; exit 1; }
command -v curl >/dev/null 2>&1 || { echo -e "${RED}Error: curl is not installed. Please install curl.${NC}"; exit 1; }

log_step "Checking backend_server health endpoint"
HEALTH_CHECK_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL$API_HEALTH_PATH")
if [ "$HEALTH_CHECK_CODE" -ne 200 ]; then
    echo -e "${RED}Error: backend_server service is not reachable or healthy at $API_URL$API_HEALTH_PATH (HTTP $HEALTH_CHECK_CODE)${NC}"
    exit 1
else
    echo -e "${GREEN}✓ backend_server service is healthy.${NC}"
fi

# --- Test Execution ---
log_step "Sending request to $API_URL$API_VIDEO_STATIC_PATH/$VIDEO_ID"

# json request body
REQUEST_BODY='{"user_id":"'"$USER_ID"'"}'

# Send GET request using curl in background and capture PID
curl -s -w "\n%{http_code}" -X GET \
    -H "Authorization: Bearer $SERVICE_TOKEN" \
    -H "Content-Type: application/json" \
    -d "$REQUEST_BODY" \
    "$API_URL$API_VIDEO_STATIC_PATH/$VIDEO_ID" > curl_response.tmp &
CURL_PID=$!

echo -e "${YELLOW}Request sent. PID: $CURL_PID. Press Ctrl+C to cancel.${NC}"

# Wait for curl to complete
wait $CURL_PID
CURL_PID=""

# Read the response from temp file
RESPONSE=$(cat curl_response.tmp)
rm -f curl_response.tmp

# Separate body and status code
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '$d')

log_step "Checking response"
check_result "$HTTP_CODE" "$RESPONSE_BODY" 200 "Video Static API Call"

echo -e "\n${YELLOW}Test script finished.${NC}"
exit $? # Exit with the status of the last check