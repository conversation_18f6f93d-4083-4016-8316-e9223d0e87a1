#!/bin/bash

# 带Logto认证的创建猫咪API测试脚本
# 服务器地址
BASE_URL="https:/api.caby.care"
LOGTO_BASE="https://brx8db.logto.app"
CLIENT_ID="m5e89cx7qhnv16ofchw9u"
REDIRECT_URI="https://api.caby.care/api/callback"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 日志函数
log() {
    local level=$1
    local message=$2
    local color=$NC
    
    case $level in
        "INFO") color=$BLUE ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        "ERROR") color=$RED ;;
        "DEBUG") color=$CYAN ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
}

# 打印成功信息
print_success() {
    echo -e "\033[32m✓ $1\033[0m"
}

# 打印错误信息
print_error() {
    echo -e "\033[31m✗ $1\033[0m"
}

# 打印信息
print_info() {
    echo -e "\033[34mℹ $1\033[0m"
}

# 打印标题
print_header() {
    echo -e "\033[36m=== $1 ===\033[0m"
}

# 保存令牌的函数
save_tokens() {
    local response=$1
    echo "$response" > .logto_tokens.json
    log "SUCCESS" "令牌已保存到 .logto_tokens.json"
    
    # 提取access_token用于后续测试
    ACCESS_TOKEN=$(echo "$response" | jq -r '.access_token')
    REFRESH_TOKEN=$(echo "$response" | jq -r '.refresh_token')
    ID_TOKEN=$(echo "$response" | jq -r '.id_token')
    USER_ID=$(echo "$response" | jq -r '.user_id')
    LOGTO_ID=$(echo "$response" | jq -r '.logto_id')
    
    echo "export LOGTO_ACCESS_TOKEN=\"$ACCESS_TOKEN\"" > logto_env.sh
    echo "export LOGTO_REFRESH_TOKEN=\"$REFRESH_TOKEN\"" >> logto_env.sh
    echo "export LOGTO_ID_TOKEN=\"$ID_TOKEN\"" >> logto_env.sh
    echo "export LOGTO_USER_ID=\"$USER_ID\"" >> logto_env.sh
    echo "export LOGTO_LOGTO_ID=\"$LOGTO_ID\"" >> logto_env.sh
    log "SUCCESS" "令牌信息已导出到 logto_env.sh"
}

# 获取访问令牌的函数
get_access_token() {
    # 检查是否已有token文件
    if [ -f ".logto_tokens.json" ]; then
        log "INFO" "发现现有token文件。您想要："
        echo -e "1) 使用现有token继续测试"
        echo -e "2) 尝试刷新token"
        echo -e "3) 获取全新token"
        read -r CHOICE
        
        if [ "$CHOICE" = "1" ]; then
            JSON_RESPONSE=$(cat .logto_tokens.json)
            log "INFO" "使用现有token继续..."
            
            # 检查token是否有效
            ACCESS_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.access_token')
            if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
                log "ERROR" "无效的token文件，请重新获取token"
                exit 1
            fi
            
            # 提取其他token信息
            REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
            ID_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.id_token')
            USER_ID=$(echo "$JSON_RESPONSE" | jq -r '.user_id')
            LOGTO_ID=$(echo "$JSON_RESPONSE" | jq -r '.logto_id')
            
        elif [ "$CHOICE" = "2" ]; then
            # 尝试刷新token
            JSON_RESPONSE=$(cat .logto_tokens.json)
            REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
            
            if [ -z "$REFRESH_TOKEN" ] || [ "$REFRESH_TOKEN" = "null" ]; then
                log "ERROR" "无效的refresh_token，请重新获取token"
                exit 1
            fi
            
            log "INFO" "尝试刷新token..."
            REFRESH_DATA="{\"refresh_token\":\"$REFRESH_TOKEN\"}"
            REFRESH_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$REFRESH_DATA" "$BASE_URL/api/refresh")
            
            # 检查刷新是否成功
            NEW_ACCESS_TOKEN=$(echo "$REFRESH_RESPONSE" | jq -r '.access_token')
            if [ -z "$NEW_ACCESS_TOKEN" ] || [ "$NEW_ACCESS_TOKEN" = "null" ]; then
                log "ERROR" "刷新token失败，请重新获取token"
                echo "$REFRESH_RESPONSE"
                exit 1
            fi
            
            log "SUCCESS" "刷新token成功"
            save_tokens "$REFRESH_RESPONSE"
            JSON_RESPONSE=$REFRESH_RESPONSE
            
            # 提取token信息
            ACCESS_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.access_token')
            REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
            ID_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.id_token')
            USER_ID=$(echo "$JSON_RESPONSE" | jq -r '.user_id')
            LOGTO_ID=$(echo "$JSON_RESPONSE" | jq -r '.logto_id')
            
        else
            # 获取新token
            get_new_token
        fi
    else
        # 没有现有token，获取新token
        log "INFO" "未发现token文件，将获取新token..."
        get_new_token
    fi
}

# 获取新token的函数
get_new_token() {
    # 生成随机state
    STATE=$(openssl rand -hex 16)
    
    # 构建授权URL
    AUTH_URL="$LOGTO_BASE/oidc/auth?client_id=$CLIENT_ID&redirect_uri=$REDIRECT_URI&response_type=code&scope=openid+profile+email+offline_access&state=$STATE"
    
    log "INFO" "请在浏览器中打开以下URL并完成登录："
    echo -e "${CYAN}$AUTH_URL${NC}"
    
    log "INFO" "登录完成后，请将重定向URL中的code参数粘贴到这里："
    read -r CODE
    
    if [ -z "$CODE" ]; then
        log "ERROR" "未提供授权码"
        exit 1
    fi
    
    # 使用授权码获取token
    log "INFO" "使用授权码获取token..."
    TOKEN_RESPONSE=$(curl -s "$BASE_URL/api/callback?code=$CODE&state=$STATE")
    
    # 检查token响应
    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
    if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
        log "ERROR" "获取token失败"
        echo "$TOKEN_RESPONSE"
        exit 1
    fi
    
    log "SUCCESS" "获取token成功"
    save_tokens "$TOKEN_RESPONSE"
    JSON_RESPONSE=$TOKEN_RESPONSE
    
    # 提取token信息
    ACCESS_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.access_token')
    REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
    ID_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.id_token')
    USER_ID=$(echo "$JSON_RESPONSE" | jq -r '.user_id')
    LOGTO_ID=$(echo "$JSON_RESPONSE" | jq -r '.logto_id')
}

# 执行带认证的HTTP请求
make_authenticated_request() {
    local method=$1
    local url=$2
    local data=$3
    
    if [ -n "$data" ]; then
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             -H "Authorization: Bearer $ACCESS_TOKEN" \
             -d "$data" \
             "$url"
    else
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             -H "Authorization: Bearer $ACCESS_TOKEN" \
             "$url"
    fi
}

# 测试用户信息
test_user_info() {
    print_header "验证用户认证"
    
    local response=$(make_authenticated_request "GET" "$BASE_URL/api/user/info" "")
    echo "用户信息: $response"
    
    if echo "$response" | grep -q '"user_id"'; then
        print_success "用户认证成功"
        return 0
    else
        print_error "用户认证失败"
        return 1
    fi
}

# 测试创建猫咪成功案例
test_create_cat_success() {
    print_header "测试创建猫咪 - 成功案例"
    
    # 测试1: 完整信息的雄性已绝育猫咪
    print_info "测试1: 创建完整信息的雄性已绝育猫咪"
    local data1='{
        "name": "Whiskers",
        "gender": 10,
        "birthday": "2020-03-15",
        "weight": 4.5,
        "breed": "British Shorthair",
        "color": "Orange Tabby",
        "photos_base64": ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="]
    }'
    
    local response1=$(make_authenticated_request "POST" "$BASE_URL/api/cats" "$data1")
    echo "Response: $response1"
    
    if echo "$response1" | grep -q '"status":"success"'; then
        print_success "测试1通过"
    else
        print_error "测试1失败"
    fi
    
    # 测试2: 最小信息的雌性未知绝育状态猫咪
    print_info "测试2: 创建最小信息的雌性未知绝育状态猫咪"
    local data2='{
        "name": "Luna",
        "gender": -1
    }'
    
    local response2=$(make_authenticated_request "POST" "$BASE_URL/api/cats" "$data2")
    echo "Response: $response2"
    
    if echo "$response2" | grep -q '"status":"success"'; then
        print_success "测试2通过"
    else
        print_error "测试2失败"
    fi
    
    # 测试3: 性别未知的猫咪
    print_info "测试3: 创建性别未知的猫咪"
    local data3='{
        "name": "Mystery Cat",
        "gender": 0,
        "birthday": "2021-01-01"
    }'
    
    local response3=$(make_authenticated_request "POST" "$BASE_URL/api/cats" "$data3")
    echo "Response: $response3"
    
    if echo "$response3" | grep -q '"status":"success"'; then
        print_success "测试3通过"
    else
        print_error "测试3失败"
    fi
}

# 测试验证错误案例
test_create_cat_validation() {
    print_header "测试创建猫咪 - 验证错误案例"
    
    # 测试4: 缺少必填字段name
    print_info "测试4: 缺少必填字段name"
    local data4='{
        "gender": 1
    }'
    
    local response4=$(make_authenticated_request "POST" "$BASE_URL/api/cats" "$data4")
    echo "Response: $response4"
    
    if echo "$response4" | grep -q '"error"'; then
        print_success "测试4通过 - 正确返回错误"
    else
        print_error "测试4失败 - 应该返回错误"
    fi
    
    # 测试5: 无效的gender编码
    print_info "测试5: 无效的gender编码"
    local data5='{
        "name": "Invalid Gender Cat",
        "gender": 99
    }'
    
    local response5=$(make_authenticated_request "POST" "$BASE_URL/api/cats" "$data5")
    echo "Response: $response5"
    
    if echo "$response5" | grep -q '"error".*Invalid gender code'; then
        print_success "测试5通过 - 正确返回gender验证错误"
    else
        print_error "测试5失败 - 应该返回gender验证错误"
    fi
    
    # 测试6: 无效的生日格式
    print_info "测试6: 无效的生日格式"
    local data6='{
        "name": "Invalid Birthday Cat",
        "gender": 1,
        "birthday": "invalid-date"
    }'
    
    local response6=$(make_authenticated_request "POST" "$BASE_URL/api/cats" "$data6")
    echo "Response: $response6"
    
    if echo "$response6" | grep -q '"error".*Invalid birthday format'; then
        print_success "测试6通过 - 正确返回生日格式错误"
    else
        print_error "测试6失败 - 应该返回生日格式错误"
    fi
}

# 测试所有性别编码
test_gender_codes() {
    print_header "测试所有性别编码"
    
    # 定义所有有效的gender编码 - 使用简单数组避免负数索引问题
    local codes=(0 1 -1 10 11 -10 -11)
    local descriptions=(
        "性别未知"
        "雄性未知绝育状态" 
        "雌性未知绝育状态"
        "雄性已绝育"
        "雄性未绝育"
        "雌性已绝育"
        "雌性未绝育"
    )
    
    for i in "${!codes[@]}"; do
        local code="${codes[$i]}"
        local desc="${descriptions[$i]}"
        
        print_info "测试性别编码: $code ($desc)"
        
        local data="{
            \"name\": \"Test Cat $code\",
            \"gender\": $code
        }"
        
        local response=$(make_authenticated_request "POST" "$BASE_URL/api/cats" "$data")
        echo "Response: $response"
        
        if echo "$response" | grep -q '"status":"success"'; then
            print_success "性别编码 $code 测试通过"
        else
            print_error "性别编码 $code 测试失败"
        fi
        
        # 添加短暂延迟避免请求过快
        sleep 1
    done
}

# 列出用户的猫咪
list_user_cats() {
    print_header "列出用户的猫咪"
    
    local response=$(make_authenticated_request "GET" "$BASE_URL/api/cats" "")
    
    if [ $? -ne 0 ] || [ -z "$response" ]; then
        print_error "获取用户猫咪列表失败"
        return 1
    fi
    
    echo "API响应:"
    echo "$response"
    echo
    
    # 检查响应是否为错误
    if echo "$response" | grep -q '"error"'; then
        print_error "API返回错误:"
        echo "$response" | jq -r '.error // .message // "未知错误"' 2>/dev/null || echo "$response"
        return 1
    fi
    
    # 尝试解析并显示猫咪列表
    local cat_count=$(echo "$response" | jq 'length' 2>/dev/null)
    
    if [ "$cat_count" -eq 0 ] 2>/dev/null; then
        print_info "当前用户还没有创建任何猫咪"
    else
        print_success "找到 $cat_count 只猫咪:"
        echo "$response" | jq -r '.[] | "- \(.name) (\(.cat_id)) - 性别编码: \(.gender), 生日: \(.birthday // "未设置"), 品种: \(.breed // "未设置"), 颜色: \(.color // "未设置")"' 2>/dev/null || {
            echo "格式化显示失败，原始数据:"
            echo "$response" | jq '.' 2>/dev/null || echo "$response"
        }
    fi
}

# 主函数
main() {
    print_header "带Logto认证的创建猫咪API测试"
    
    # 检查服务器是否运行
    if ! curl -s "$BASE_URL/api/health" > /dev/null; then
        print_error "服务器无法访问，请确保服务器在 $BASE_URL 运行"
        exit 1
    fi
    
    print_success "服务器连接正常"
    
    # 检查jq是否安装
    if ! command -v jq &> /dev/null; then
        print_error "需要安装jq来解析JSON响应。请运行: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
        exit 1
    fi
    
    # 获取访问令牌
    print_header "获取访问令牌"
    get_access_token
    
    # 显示token信息
    log "INFO" "Token信息："
    log "INFO" "Access Token: ${ACCESS_TOKEN:0:20}..."
    log "INFO" "User ID: $USER_ID"
    log "INFO" "Logto ID: $LOGTO_ID"
    
    # 验证用户认证
    test_user_info
    
    if [ $? -eq 0 ]; then
        # 运行猫咪创建测试
        test_create_cat_success
        echo
        test_create_cat_validation
        echo
        test_gender_codes
        echo
        list_user_cats
    else
        print_error "用户认证失败，无法继续测试"
        exit 1
    fi
    
    print_header "测试完成"
    
    # 显示gender编码说明
    echo
    print_header "Gender编码说明"
    echo "0: 性别未知"
    echo "1: 雄性未知绝育状态"
    echo "-1: 雌性未知绝育状态"
    echo "10: 雄性已绝育"
    echo "11: 雄性未绝育"
    echo "-10: 雌性已绝育"
    echo "-11: 雌性未绝育"
}

# 运行主函数
main "$@" 
