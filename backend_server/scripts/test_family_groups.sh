#!/bin/bash

# Test script for family group functionality

# Default environment variables
API_BASE="http://localhost:5678"
ACCESS_TOKEN=""
USER_ID="022b442c1f021000"
DEVICE_ID="202505160285582a54021000"
GROUP_ID="fg_028a5f4e85821000"
MEMBER_ID=""

# Debug flag (set to 1 to enable detailed debug output)
DEBUG=1

# Terminal colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    
    case $level in
        "INFO") echo -e "${BLUE}[INFO]${NC} $timestamp - $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $timestamp - $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $timestamp - $message" ;;
        "DEBUG") 
            if [ "$DEBUG" = "1" ]; then
                echo -e "${YELLOW}[DEBUG]${NC} $timestamp - $message"
            fi
            ;;
        *) echo -e "$timestamp - $message" ;;
    esac
}

# Function to check if jq is installed
check_dependencies() {
    if ! command -v jq &> /dev/null; then
        log "ERROR" "jq is required but not installed. Please install jq first."
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log "ERROR" "curl is required but not installed. Please install curl first."
        exit 1
    fi
}

# Function to clean up response data for better parsing
cleanup_response() {
    local response="$1"
    # Remove ANSI color codes and other control characters
    echo "$response" | sed 's/\x1B\[[0-9;]*[JKmsu]//g' | tr -dc '[:print:]\n'
}

# Function to extract clean JSON from response
extract_json() {
    local response="$1"
    
    # First clean up the response to remove any ANSI codes or control chars
    local cleaned_response=$(cleanup_response "$response")
    
    # Direct method: try to extract a complete JSON object using grep and sed
    local json_pattern=$(echo "$cleaned_response" | grep -o '{.*}')
    if [ -n "$json_pattern" ] && echo "$json_pattern" | jq . &> /dev/null; then
        echo "$json_pattern"
        return 0
    fi
    
    # Try to find a complete JSON pattern in the response using regex
    if [[ "$cleaned_response" =~ (\{.*\}) ]]; then
        # Extract the matched JSON part using bash regex
        local json_match="${BASH_REMATCH[1]}"
        if echo "$json_match" | jq . &> /dev/null; then
            echo "$json_match"
            return 0
        fi
    fi
    
    # If that didn't work, try with the original approach
    # Find the first { character position
    local start_pos=$(echo "$cleaned_response" | grep -b -o '{' | head -n 1 | cut -d: -f1)
    
    if [ -n "$start_pos" ]; then
        local json_extract=$(echo "$cleaned_response" | tail -c +$((start_pos+1)))
        
        # Verify this is valid JSON
        if echo "$json_extract" | jq . &> /dev/null; then
            echo "$json_extract"
            return 0
        fi
    fi
    
    # Last attempt - try to fix common JSON issues and force-extract
    local fixed_json=$(echo "$cleaned_response" | sed -n '/^{/,/}$/p')
    if [ -n "$fixed_json" ] && echo "$fixed_json" | jq . &> /dev/null; then
        echo "$fixed_json"
        return 0
    fi
    
    log "ERROR" "Could not extract valid JSON from response"
    log "DEBUG" "Full cleaned response: $cleaned_response"
    return 1
}

# Function to handle API requests
make_request() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=${4:-""}
    local additional_headers=${5:-""}
    
    local auth_header=""
    if [ -n "$ACCESS_TOKEN" ]; then
        auth_header="-H \"Authorization: Bearer $ACCESS_TOKEN\""
    fi
    
    log "INFO" "Making $method request to $endpoint - $description"
    
    # Construct the curl command
    local cmd="curl -s -X $method"
    
    # Add auth header if present
    if [ -n "$auth_header" ]; then
        cmd="$cmd $auth_header"
    fi
    
    # Add additional headers if present
    if [ -n "$additional_headers" ]; then
        cmd="$cmd $additional_headers"
    fi
    
    # Always set content type for consistency
    cmd="$cmd -H \"Content-Type: application/json\""
    
    # Add data if present
    if [ -n "$data" ]; then
        cmd="$cmd -d '$data'"
    fi
    
    # Add the endpoint
    cmd="$cmd \"${API_BASE}${endpoint}\""
    
    # Log the full command if in debug mode
    if [ "$DEBUG" = "1" ]; then
        log "DEBUG" "Executing command: $cmd"
    fi
    
    # Execute the command and capture the raw response
    local raw_response=$(eval $cmd)
    
    # Remove any non-JSON content that might be mixed with the JSON response
    local clean_response=""
    
    # Try to extract clean JSON from raw response
    if [[ "$raw_response" =~ (\{.*\}) ]]; then
        # Extract the matched JSON part using bash regex
        clean_response="${BASH_REMATCH[1]}"
    else
        # If regex doesn't work, try grep
        local json_start=$(echo "$raw_response" | grep -b -o '{' | head -n 1 | cut -d: -f1)
        if [ -n "$json_start" ]; then
            clean_response=$(echo "$raw_response" | tail -c +$((json_start+1)))
        else
            clean_response="$raw_response"
        fi
    fi
    
    # Check if response is valid JSON
    if echo "$clean_response" | jq . &> /dev/null; then
        # If it's valid JSON, show it formatted
        log "SUCCESS" "Response received:"
        echo "$clean_response" | jq .
        
        # Check for error in the response
        local error_msg=$(echo "$clean_response" | jq -r '.error')
        if [ -n "$error_msg" ] && [ "$error_msg" != "null" ]; then
            log "ERROR" "API error: $error_msg"
        fi
        
        # In debug mode, show the structure paths
        if [ "$DEBUG" = "1" ]; then
            log "DEBUG" "Response keys: $(echo "$clean_response" | jq 'keys')"
        fi
        
        # Use the clean response for further processing
        raw_response="$clean_response"
    else
        log "ERROR" "Invalid JSON response received:"
        echo "$raw_response"
    fi
    
    # Return the raw response for further processing
    echo "$raw_response"
}

# Test login function removed - using hardcoded credentials

# Test creating a family group
test_create_family_group() {
    log "INFO" "Testing family group creation..."
    
    local group_name="Test Family Group"
    local group_data="{\"group_name\":\"$group_name\",\"description\":\"Test family group created by script\"}"
    
    local response=$(make_request "POST" "/api/family-groups?user_id=$USER_ID" "Create family group" "$group_data")
    
    # First attempt: Try to directly extract the group_id from the raw response
    GROUP_ID=$(echo "$response" | grep -o '"group_id":"[^"]*"' | head -1 | sed 's/"group_id":"//g' | sed 's/"//g')
    
    if [ -z "$GROUP_ID" ]; then
        log "DEBUG" "Could not extract group_id using grep, trying JSON parser..."
        
        # Second attempt: Extract clean JSON from response and parse it
        local json_response=$(extract_json "$response")
        if [ $? -ne 0 ]; then
            log "ERROR" "Failed to extract JSON from response"
            return 1
        fi
        
        log "DEBUG" "Extracted JSON: $json_response"
        
        # Extract group_id from clean JSON
        GROUP_ID=$(echo "$json_response" | jq -r '.data.group_id')
    fi
    
    if [ -z "$GROUP_ID" ] || [ "$GROUP_ID" == "null" ]; then
        log "WARNING" "Could not extract group_id from response"
        log "ERROR" "Failed to create family group"
        return 1
    fi
    
    log "SUCCESS" "Created family group with ID: $GROUP_ID"
}

# Test getting family group details
test_get_family_group() {
    log "INFO" "Testing getting family group details..."
    
    if [ -z "$GROUP_ID" ]; then
        log "ERROR" "GROUP_ID is not set. Please create a family group first."
        return
    fi
    
    make_request "GET" "/api/family-groups/$GROUP_ID?user_id=$USER_ID" "Get family group details"
}

# Test listing user's family groups
test_list_user_family_groups() {
    log "INFO" "Testing listing user's family groups..."
    
    make_request "GET" "/api/family-groups?user_id=$USER_ID" "List user family groups"
}

# Test adding a device to family group
test_add_device_to_group() {
    log "INFO" "Testing adding a device to family group..."
    
    if [ -z "$GROUP_ID" ]; then
        log "ERROR" "GROUP_ID is not set. Please create a family group first."
        return
    fi
    
    log "INFO" "Using device with ID: $DEVICE_ID"
    
    # Add device to family group
    local device_data="{\"device_id\":\"$DEVICE_ID\"}"
    local response=$(make_request "POST" "/api/family-groups/$GROUP_ID/devices?user_id=$USER_ID" "Add device to family group" "$device_data")
    
    # Check if the operation was successful by searching for success pattern
    if [[ "$response" =~ "success" ]]; then
        log "SUCCESS" "Added device $DEVICE_ID to family group"
        return 0
    fi
    
    # If we couldn't determine success directly, try JSON extraction
    local json_response=$(extract_json "$response")
    if [ $? -eq 0 ]; then
        local status=$(echo "$json_response" | jq -r '.status')
        if [ "$status" = "success" ]; then
            log "SUCCESS" "Added device $DEVICE_ID to family group"
            return 0
        fi
    fi
    
    log "ERROR" "Failed to add device to family group"
    return 1
}

# Test adding a member to family group
test_add_member_to_group() {
    log "INFO" "Testing adding a member to family group..."
    
    if [ -z "$GROUP_ID" ]; then
        log "ERROR" "GROUP_ID is not set. Please create a family group first."
        return
    fi
    
    read -p "Enter member user ID: " member_id
    MEMBER_ID=$member_id
    
    local member_data="{\"user_id\":\"$MEMBER_ID\",\"nickname\":\"Test Member\",\"role\":2}"
    make_request "POST" "/api/family-groups/$GROUP_ID/members?user_id=$USER_ID" "Add member to family group" "$member_data"
}

# Test listing accessible devices
test_list_accessible_devices() {
    log "INFO" "Testing listing all accessible devices..."
    
    make_request "GET" "/api/devices/accessible?user_id=$USER_ID" "List all accessible devices"
}

# Test accessing device as member
test_access_device_as_member() {
    log "INFO" "Testing accessing device as a family member..."
    
    if [ -z "$DEVICE_ID" ]; then
        log "ERROR" "DEVICE_ID is not set. Please add a device to group first."
        return
    fi
    
    if [ -z "$MEMBER_ID" ]; then
        log "ERROR" "MEMBER_ID is not set. Please add a member to group first."
        return
    fi
    
    # Test accessing device directly (should fail if device is not owned by member)
    log "INFO" "Testing direct access to device (should fail if not owner)..."
    make_request "GET" "/api/devices/$DEVICE_ID?user_id=$MEMBER_ID" "Access device directly as member"
    
    # Test accessing device through family group (should succeed)
    log "INFO" "Testing access to device via family group (should succeed)..."
    make_request "GET" "/api/devices/accessible?user_id=$MEMBER_ID" "List accessible devices as member"
}

# Test removing device from family group
test_remove_device_from_group() {
    log "INFO" "Testing removing device from family group..."
    
    if [ -z "$GROUP_ID" ] || [ -z "$DEVICE_ID" ]; then
        log "ERROR" "GROUP_ID or DEVICE_ID is not set."
        return
    fi
    
    make_request "DELETE" "/api/family-groups/$GROUP_ID/devices/$DEVICE_ID?user_id=$USER_ID" "Remove device from family group"
}

# Test removing member from family group
test_remove_member_from_group() {
    log "INFO" "Testing removing member from family group..."
    
    if [ -z "$GROUP_ID" ] || [ -z "$MEMBER_ID" ]; then
        log "ERROR" "GROUP_ID or MEMBER_ID is not set."
        return
    fi
    
    make_request "DELETE" "/api/family-groups/$GROUP_ID/members/$MEMBER_ID?user_id=$USER_ID" "Remove member from family group"
}

# Test deleting family group
test_delete_family_group() {
    log "INFO" "Testing deleting family group..."
    
    if [ -z "$GROUP_ID" ]; then
        log "ERROR" "GROUP_ID is not set. Please create a family group first."
        return
    fi
    
    make_request "DELETE" "/api/family-groups/$GROUP_ID?user_id=$USER_ID" "Delete family group"
}

# Test creating family group invitation
test_create_invitation() {
    log "INFO" "Testing creating family group invitation..."
    
    if [ -z "$GROUP_ID" ]; then
        log "ERROR" "GROUP_ID is not set. Please create a family group first."
        return
    fi
    
    read -p "Enter invitee user ID: " invitee_id
    if [ -z "$invitee_id" ]; then
        log "ERROR" "Invitee user ID is required."
        return
    fi
    
    # Create invitation
    local invitation_data="{\"invitee_id\":\"$invitee_id\",\"role\":0}"
    local response=$(make_request "POST" "/api/family-groups/$GROUP_ID/invitations?user_id=$USER_ID" "Create invitation" "$invitation_data" "200" "true")
    
    # Extract invitation ID if successful
    local json_response=$(extract_json "$response")
    if [ $? -eq 0 ]; then
        local invitation_id=$(echo "$json_response" | jq -r '.data.invitation_id')
        if [[ -n "$invitation_id" && "$invitation_id" != "null" ]]; then
            log "SUCCESS" "Created invitation with ID: $invitation_id"
            INVITATION_ID=$invitation_id
            return 0
        fi
    fi
    
    log "ERROR" "Failed to create invitation"
    return 1
}

# Test listing invitations
test_list_invitations() {
    log "INFO" "Testing listing invitations..."
    
    # List sent invitations
    log "INFO" "Listing sent invitations..."
    make_request "GET" "/api/family-groups/invitations/sent?user_id=$USER_ID" "List sent invitations" "" "200" "true"
    
    # Ask if user wants to list received invitations
    read -p "Do you want to list received invitations? (y/n): " answer
    if [[ "$answer" == "y" || "$answer" == "Y" ]]; then
        make_request "GET" "/api/family-groups/invitations/received?user_id=$USER_ID" "List received invitations" "" "200" "true"
    fi
}

# Main function
main() {
    check_dependencies
    
    # Parse command line arguments
    while getopts ":a:t:u:h" opt; do
        case ${opt} in
            a )
                API_BASE=$OPTARG
                ;;
            t )
                ACCESS_TOKEN=$OPTARG
                ;;
            u )
                USER_ID=$OPTARG
                ;;
            h )
                echo "Usage: $0 [-a API_BASE] [-t ACCESS_TOKEN] [-u USER_ID]"
                exit 0
                ;;
            \? )
                echo "Invalid option: $OPTARG" 1>&2
                exit 1
                ;;
            : )
                echo "Invalid option: $OPTARG requires an argument" 1>&2
                exit 1
                ;;
        esac
    done
    
    log "INFO" "Starting family group API test script..."
    log "INFO" "API base URL: $API_BASE"
    log "INFO" "Using USER_ID: $USER_ID"
    log "INFO" "Using DEVICE_ID: $DEVICE_ID"
    
    # Show test menu
    while true; do
        echo ""
        echo "==== Family Group Test Menu ===="
        echo "1. Create family group"
        echo "2. Get family group details"
        echo "3. List user family groups"
        echo "4. Add device to family group"
        echo "5. Add member to family group" 
        echo "6. List accessible devices"
        echo "7. Test device access as member"
        echo "8. Remove device from family group"
        echo "9. Remove member from family group"
        echo "10. Delete family group"
        echo "11. Create invitation"
        echo "12. List invitations"
        echo "0. Exit"
        echo "================================"
        
        read -p "Select an option: " option
        echo ""
        
        case $option in
            1) test_create_family_group ;;
            2) test_get_family_group ;;
            3) test_list_user_family_groups ;;
            4) test_add_device_to_group ;;
            5) test_add_member_to_group ;;
            6) test_list_accessible_devices ;;
            7) test_access_device_as_member ;;
            8) test_remove_device_from_group ;;
            9) test_remove_member_from_group ;;
            10) test_delete_family_group ;;
            11) test_create_invitation ;;
            12) test_list_invitations ;;
            0) exit 0 ;;
            *) log "ERROR" "Invalid option." ;;
        esac
    done
}

# Run main function
main "$@"
