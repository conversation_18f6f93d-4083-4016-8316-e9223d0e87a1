#!/bin/bash

CONFIG_DIR="/app/config"
CONFIG_FILE="${CONFIG_DIR}/config.yaml"
DEFAULT_CONFIG_FILE="${CONFIG_DIR}/config.yaml.default"

# 确保配置目录存在
mkdir -p ${CONFIG_DIR}

# 检查是否已存在配置文件
if [ ! -f "${CONFIG_FILE}" ]; then
    echo "未找到配置文件，将创建默认配置..."
    
    # 如果没有设置密码，生成随机密码
    if [ -z "${MYSQL_PASSWORD}" ]; then
        MYSQL_PASSWORD=$(tr -dc 'a-zA-Z0-9' < /dev/urandom | fold -w 12 | head -n 1)
        echo "已生成随机MySQL密码: ${MYSQL_PASSWORD}"
    fi
    
    if [ -z "${MINIO_ROOT_PASSWORD}" ]; then
        MINIO_ROOT_PASSWORD=$(tr -dc 'a-zA-Z0-9' < /dev/urandom | fold -w 16 | head -n 1)
        echo "已生成随机MinIO密码: ${MINIO_ROOT_PASSWORD}"
    fi
    
    # 创建配置文件
    cat > ${CONFIG_FILE} <<EOF
minio:
  endpoint: "***************:9000"
  access_key: "${MINIO_ROOT_USER:-minioadmin}"
  secret_key: "${MINIO_ROOT_PASSWORD:-minioadmin}"
  use_ssl: false

mysql:
  host: "***************"
  port: 3306
  user: "${MYSQL_USER:-cattoilet}"
  password: "${MYSQL_PASSWORD}"
  database: "${MYSQL_DATABASE:-cat_toilet_db}"
EOF
    
    # 保存一份默认配置副本
    cp ${CONFIG_FILE} ${DEFAULT_CONFIG_FILE}
    echo "已创建默认配置文件"
else
    echo "使用现有配置文件: ${CONFIG_FILE}"
    
    # 从配置文件读取数据库信息
    MYSQL_USER=$(grep -A 5 "mysql:" ${CONFIG_FILE} | grep "user:" | awk '{print $2}' | tr -d '"')
    MYSQL_PASSWORD=$(grep -A 5 "mysql:" ${CONFIG_FILE} | grep "password:" | awk '{print $2}' | tr -d '"')
    MYSQL_DATABASE=$(grep -A 5 "mysql:" ${CONFIG_FILE} | grep "database:" | awk '{print $2}' | tr -d '"')
    
    # 从配置文件读取MinIO信息
    MINIO_ROOT_USER=$(grep -A 5 "minio:" ${CONFIG_FILE} | grep "access_key:" | awk '{print $2}' | tr -d '"')
    MINIO_ROOT_PASSWORD=$(grep -A 5 "minio:" ${CONFIG_FILE} | grep "secret_key:" | awk '{print $2}' | tr -d '"')
fi

# Start MySQL
echo "Starting MySQL..."
usermod -d /var/lib/mysql/ mysql
mkdir -p /data/mysql
chown -R mysql:mysql /data/mysql

# Check if we need to reset the MySQL data directory
if [ "${MYSQL_RESET:-false}" = "true" ]; then
    echo "MYSQL_RESET set to true, cleaning MySQL data directory..."
    rm -rf /data/mysql/*
    echo "MySQL data directory cleared."
fi

# Remove any stale PID files (important when container restarts)
find /data/mysql -name "*.pid" -delete
find /var/run/mysqld -name "*.pid" -delete
echo "Removed any stale PID files"

# Create MySQL configuration file to ensure TCP connections work properly
cat > /etc/mysql/conf.d/docker.cnf <<EOF
[mysqld]
skip-host-cache
skip-name-resolve
bind-address=0.0.0.0
port=3306
socket=/var/run/mysqld/mysqld.sock
pid-file=/var/run/mysqld/mysqld.pid
general_log=1
general_log_file=/var/log/mysql/mysql.log
log_error=/var/log/mysql/error.log
# Set lower innodb buffer pool size to ensure stability
innodb_buffer_pool_size=256M
# Don't use host name resolution
performance_schema_max_digest_length=32
performance_schema_max_sql_text_length=4096
EOF

# Create directories for socket file and logs
mkdir -p /var/run/mysqld /var/log/mysql
chown -R mysql:mysql /var/run/mysqld /var/log/mysql
chmod 777 /var/run/mysqld

# Initialize MySQL data directory if it's empty
if [ -z "$(ls -A /data/mysql 2>/dev/null)" ]; then
    echo "Initializing MySQL data directory..."
    mysqld --initialize-insecure --user=mysql --datadir=/data/mysql
    echo "MySQL initialization completed."
fi

# Start MySQL with the external data directory and configuration
echo "Starting MySQL daemon..."
mysqld --defaults-file=/etc/mysql/conf.d/docker.cnf --user=mysql --datadir=/data/mysql &
MYSQL_PID=$!

# Wait for MySQL to be ready with a timeout
echo "Waiting for MySQL to start..."
MAX_ATTEMPTS=30
ATTEMPTS=0
while [ $ATTEMPTS -lt $MAX_ATTEMPTS ]; do
    if mysqladmin ping -h 127.0.0.1 -u root --silent; then
        echo "MySQL is up and running!"
        break
    fi
    
    # Check if MySQL process is still running
    if ! ps -p $MYSQL_PID > /dev/null; then
        echo "ERROR: MySQL process died. Checking logs..."
        if [ -f /var/log/mysql/error.log ]; then
            echo "=== MySQL Error Log ==="
            cat /var/log/mysql/error.log
            echo "======================="
        fi
        
        if [ -f /var/log/mysql/mysql.log ]; then
            echo "=== MySQL General Log ==="
            tail -n 50 /var/log/mysql/mysql.log
            echo "======================="
        fi
        
        echo "To fix this issue, try restarting with MYSQL_RESET=true environment variable"
        echo "Failed to start MySQL. Exiting."
        exit 1
    fi
    
    ATTEMPTS=$((ATTEMPTS+1))
    echo "Waiting for MySQL to start (attempt $ATTEMPTS of $MAX_ATTEMPTS)..."
    sleep 2
done

if [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; then
    echo "Timeout waiting for MySQL to start. MySQL status:"
    mysqladmin status || echo "MySQL is not responding"
    echo "MySQL process status:"
    ps aux | grep mysql
    echo "Checking MySQL error logs:"
    find /var/log/mysql /data/mysql -name "*.err" -o -name "*.log" | xargs -I{} sh -c 'echo "=== {} ==="; tail -n 20 {}'
    echo "Failed to start MySQL within timeout period. Continuing anyway, but services may be unreliable."
fi

# 仅当INIT_DB设置为true或未设置时初始化数据库
if [ "${INIT_DB:-true}" = "true" ] && [ "${SETUP_MODE:-auto}" = "auto" ]; then
    echo "Setting up MySQL database and user..."
    mysql -u root <<EOF
CREATE DATABASE IF NOT EXISTS ${MYSQL_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '${MYSQL_USER}'@'%' IDENTIFIED BY '${MYSQL_PASSWORD}';
GRANT ALL PRIVILEGES ON ${MYSQL_DATABASE}.* TO '${MYSQL_USER}'@'%';
FLUSH PRIVILEGES;
EOF

    # Import schema if needed
    if [ -f /app/database/mysql.sql ]; then
        echo "Importing database schema..."
        mysql -u root ${MYSQL_DATABASE} < /app/database/mysql.sql
    fi
else
    echo "跳过数据库初始化 (INIT_DB=${INIT_DB}, SETUP_MODE=${SETUP_MODE})"
fi

# Start MinIO in background
echo "Starting MinIO..."
mkdir -p /data/minio

# Export MinIO environment variables
export MINIO_ROOT_USER=${MINIO_ROOT_USER}
export MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}

/usr/local/bin/minio server /data/minio --console-address ":9001" &

# Wait for MinIO to start
echo "Waiting for MinIO to start..."
sleep 5

# 仅在自动模式下创建存储桶
if [ "${SETUP_MODE:-auto}" = "auto" ]; then
    echo "Setting up MinIO buckets..."
    wget https://dl.min.io/client/mc/release/linux-amd64/mc -O /usr/local/bin/mc
    chmod +x /usr/local/bin/mc
    mc config host add myminio http://***************:9000 ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD}
else
    echo "跳过MinIO存储桶创建 (SETUP_MODE=${SETUP_MODE})"
fi

# 检查MySQL状态
echo "Checking MySQL status..."
mysqladmin -u root status || echo "MySQL status check failed"
echo "MySQL process status:"
ps aux | grep mysql

# 测试MySQL连接
echo "Testing MySQL connection..."
mysql -u ${MYSQL_USER} -p${MYSQL_PASSWORD} -e "SELECT VERSION();" ${MYSQL_DATABASE} || echo "MySQL connection test failed"

# Test TCP/IP connection explicitly (important for external clients)
echo "Testing TCP/IP connection to MySQL..."
mysql -u ${MYSQL_USER} -p${MYSQL_PASSWORD} -h 127.0.0.1 -P 3306 -e "SELECT 'TCP connection successful';" ${MYSQL_DATABASE} || echo "TCP connection test failed"

# 显示运行信息
echo "========================================"
echo "服务初始化完成！"
echo "MySQL 运行在 ***************:3306 (TCP/IP)"
echo "MinIO API 运行在 http://***************:9000"
echo "MinIO Console 运行在 http://***************:9001"
echo "后端API 运行在 http://***************:5678"
echo "logto core 运行在 http://***************:3001/"
echo "logto admin 运行在 http://***************:3002/"
echo "========================================"

# Start the backend server
echo "Starting backend server with config at ${CONFIG_PATH}"
cd /app
./cabycare-server
