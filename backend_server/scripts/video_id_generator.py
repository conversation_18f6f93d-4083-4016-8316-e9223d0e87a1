#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# video_id_generator.py - Utility for generating and parsing video IDs

import argparse
import hashlib
import sys
import datetime
import re
import time
from datetime import timezone, timedelta
from typing import Optional, Union
from dateutil import parser as date_parser


def generate_video_id(device_id: str, user_id: str, timestamp: int) -> str:
    """
    Generate a video ID compatible with the backend Go implementation

    Args:
        device_id: Device ID string
        user_id: User ID string
        timestamp: Unix timestamp in seconds

    Returns:
        A generated video ID string
    """
    # Get first 10 chars of device_id MD5 hash
    device_hash = hashlib.md5(device_id.encode()).hexdigest()[:10]

    # Get first 6 chars of user_id MD5 hash
    user_hash = hashlib.md5(user_id.encode()).hexdigest()[:6]

    # Convert timestamp to hex
    time_hex = format(timestamp, 'x')

    # Concatenate all parts to form the video ID
    video_id = f"{device_hash}{user_hash}{time_hex}"

    return video_id


def parse_time(time_str: str) -> int:
    """
    Parse time string into Unix timestamp

    Args:
        time_str: Time string in various formats

    Returns:
        Unix timestamp in seconds
    """
    try:
        dt = date_parser.parse(time_str)
        return int(dt.timestamp())
    except Exception as e:
        print(f"Error parsing time: {e}")
        sys.exit(1)


def timestamp_to_formatted_date(unix_timestamp, tz_offset=8):
    """
    Convert a Unix timestamp to a formatted date string with timezone adjustment.
    
    Args:
        unix_timestamp: Unix timestamp (seconds since epoch)
        tz_offset: Timezone offset in hours (default: +8 for China)
        
    Returns:
        Formatted date string: YYYY-MM-DD_HH-MM-SS_hls
    """
    # Create datetime object with UTC timezone
    dt_utc = datetime.datetime.fromtimestamp(unix_timestamp, tz=timezone.utc)
    
    # Apply timezone offset
    tz = timezone(timedelta(hours=tz_offset))
    dt_local = dt_utc.astimezone(tz)
    
    # Format as required
    formatted_date = dt_local.strftime("%Y-%m-%d_%H-%M-%S_hls")
    return formatted_date


def formatted_date_to_timestamp(formatted_date):
    """
    Convert a formatted date string back to Unix timestamp.
    
    Args:
        formatted_date: Date string in format YYYY-MM-DD_HH-MM-SS_hls
        
    Returns:
        Unix timestamp (seconds since epoch)
    """
    # Remove "_hls" suffix
    date_str = formatted_date.replace("_hls", "")
    
    # Parse the datetime
    dt = datetime.datetime.strptime(date_str, "%Y-%m-%d_%H-%M-%S")
    
    # Convert to timestamp
    return int(dt.timestamp())


def generate_video_id_with_current_time():
    """
    Generate a new video ID based on current timestamp.
    
    Returns:
        Hexadecimal string video ID
    """
    # Get current timestamp in milliseconds
    current_time_ms = int(time.time() * 1000)
    
    # Convert to hexadecimal string
    hex_id = format(current_time_ms, 'x')
    
    # Pad with random chars to make it 24 characters long
    import random
    while len(hex_id) < 24:
        hex_id = hex_id + format(random.randint(0, 15), 'x')
    
    return hex_id


def main():
    parser = argparse.ArgumentParser(description="Generate a video ID for debugging purposes")
    parser.add_argument('--device_id', required=True, help="Device ID string")
    parser.add_argument('--user_id', required=True, help="User ID string")
    parser.add_argument('--time', help="Time in UTC format (e.g., '2023-01-01T12:00:00Z' or '2023-01-01T12:00:00+08:00')")
    parser.add_argument('--timestamp', type=int, help="Unix timestamp in seconds")

    args = parser.parse_args()

    # Validate that either --time or --timestamp is provided
    if args.time is None and args.timestamp is None:
        print("Error: Either --time or --timestamp must be provided")
        parser.print_help()
        sys.exit(1)

    # Get timestamp from either direct input or parsed time string
    if args.timestamp is not None:
        timestamp = args.timestamp
        time_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%dT%H:%M:%S%z')
    else:
        timestamp = parse_time(args.time)
        time_str = args.time

    # Generate the video ID
    video_id = generate_video_id(args.device_id, args.user_id, timestamp)

    # Print detailed output
    print("\n=== Video ID Generator ===")
    print(f"Device ID:    {args.device_id}")
    print(f"User ID:      {args.user_id}")
    print(f"Time:         {time_str}")
    print(f"Timestamp:    {timestamp}")
    print(f"Device Hash:  {hashlib.md5(args.device_id.encode()).hexdigest()[:10]}")
    print(f"User Hash:    {hashlib.md5(args.user_id.encode()).hexdigest()[:6]}")
    print(f"Time Hex:     {format(timestamp, 'x')}")
    print(f"Video ID:     {video_id}")
    print("========================\n")

    return video_id


if __name__ == "__main__":
    main()
