#!/bin/bash

# Script to check device status

# Default values
SERVER_URL="http://localhost:5678"
DEVICE_ID=""

# Display usage information
function show_usage {
    echo "Usage: $0 -d <device_id> [-U <server_url>]"
    echo "  -d  Device ID (required)"
    echo "  -U  Server URL (default: http://localhost:5678)"
    echo "  -h  Display this help message"
    exit 1
}

# Parse command line arguments
while getopts "d:U:h" opt; do
    case $opt in
        d) DEVICE_ID="$OPTARG" ;;
        U) SERVER_URL="$OPTARG" ;;
        h) show_usage ;;
        *) show_usage ;;
    esac
done

# Check for required parameters
if [ -z "$DEVICE_ID" ]; then
    echo "Error: Device ID is required."
    show_usage
fi

# Display test information
echo "Checking device status"
echo "Server URL: $SERVER_URL"
echo "Device ID: $DEVICE_ID"

# Make the request
echo -e "\nSending status request..."
RESPONSE=$(curl -s -w "\nHTTP Status: %{http_code}" \
    "$SERVER_URL/api/devices/$DEVICE_ID/status")

# Extract the HTTP status code
HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP Status:" | cut -d' ' -f3)
RESPONSE_BODY=$(echo "$RESPONSE" | grep -v "HTTP Status:")

echo -e "\nResponse:"
echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"
echo ""

# Check if the request was successful
if [ "$HTTP_STATUS" -eq 200 ]; then
    echo "✅ Test passed! Device status retrieved successfully."
    
    # Extract and display key information if jq is available
    if command -v jq &> /dev/null; then
        ONLINE=$(echo "$RESPONSE_BODY" | jq -r .online)
        NAME=$(echo "$RESPONSE_BODY" | jq -r .name)
        MODEL=$(echo "$RESPONSE_BODY" | jq -r .model)
        FIRMWARE=$(echo "$RESPONSE_BODY" | jq -r .firmware)
        LAST_HEARTBEAT=$(echo "$RESPONSE_BODY" | jq -r .last_heartbeat)
        
        echo "📱 Device Information:"
        echo "   - Name: ${NAME}"
        echo "   - Model: ${MODEL}"
        echo "   - Firmware: ${FIRMWARE}"
        echo "   - Status: $([ "$ONLINE" = "true" ] && echo "🟢 ONLINE" || echo "🔴 OFFLINE")"
        echo "   - Last Heartbeat: ${LAST_HEARTBEAT}"
        
        # Display network information if available
        IPV4=$(echo "$RESPONSE_BODY" | jq -r .ipv4)
        IPV6=$(echo "$RESPONSE_BODY" | jq -r .ipv6)
        if [ "$IPV4" != "null" ]; then
            echo "   - IPv4: ${IPV4}"
        fi
        if [ "$IPV6" != "null" ]; then
            echo "   - IPv6: ${IPV6}"
        fi
    fi
else
    echo "❌ Test failed! HTTP Status: $HTTP_STATUS"
fi