#!/bin/bash

# 彩色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认参数
SERVER_URL=${1:-"http://144.126.146.223:5678"}
DEVICE_ID=${2:-"202502270220f7cbb4421000"}
USER_ID=${3:-"022b605dc3421000"}
OBJECT_PATH=${4:-"2025-05-01_00-31-14_hls"}

# 显示参数
echo -e "${BLUE}测试单个记录导入...${NC}"
echo -e "${BLUE}服务器地址: ${SERVER_URL}${NC}"
echo -e "${BLUE}设备ID: ${DEVICE_ID}${NC}"
echo -e "${BLUE}用户ID: ${USER_ID}${NC}"
echo -e "${BLUE}对象路径: ${OBJECT_PATH}${NC}"

# 准备JSON请求体
JSON_REQUEST=$(cat <<EOF
{
  "object_path": "$OBJECT_PATH",
  "device_id": "$DEVICE_ID",
  "user_id": "$USER_ID",
  "weight_litter": 0,
  "weight_cat": 0,
  "weight_waste": 0
}
EOF
)

# 调用API
echo -e "${GREEN}发送导入请求...${NC}"
RESPONSE=$(curl -s -X POST "${SERVER_URL}/api/temp/import-record" \
  -H "Content-Type: application/json" \
  -d "$JSON_REQUEST" \
  -w "\n%{http_code}")

# 提取HTTP状态码和响应体
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '$d')

# 保存响应到文件
TIMESTAMP=$(date +%Y%m%d%H%M%S)
RESULT_FILE="import_single_result_${TIMESTAMP}.json"
echo "$RESPONSE_BODY" > "$RESULT_FILE"

# 根据状态码判断是否成功
if [ "$HTTP_CODE" -eq 200 ]; then
  echo -e "${GREEN}导入成功! 状态码: ${HTTP_CODE}${NC}"

  # 如果安装了jq，使用它解析结果
  if command -v jq &> /dev/null; then
    VIDEO_ID=$(echo "$RESPONSE_BODY" | jq -r '.video_id')
    START_TIME=$(echo "$RESPONSE_BODY" | jq -r '.start_time')

    echo -e "${BLUE}VideoID: ${VIDEO_ID}${NC}"
    echo -e "${BLUE}StartTime: ${START_TIME}${NC}"

    # 尝试格式化时间戳
    if command -v date >/dev/null 2>&1; then
      if date -r "$START_TIME" +"%Y-%m-%d %H:%M:%S" >/dev/null 2>&1; then
        # macOS 方式
        FORMATTED_TIME=$(date -r "$START_TIME" +"%Y-%m-%d %H:%M:%S")
      elif date -d "@$START_TIME" +"%Y-%m-%d %H:%M:%S" >/dev/null 2>&1; then
        # Linux 方式
        FORMATTED_TIME=$(date -d "@$START_TIME" +"%Y-%m-%d %H:%M:%S")
      fi

      if [ -n "$FORMATTED_TIME" ]; then
        echo -e "${BLUE}格式化时间: ${FORMATTED_TIME}${NC}"
      fi
    fi

    # 如果有EndTime
    if echo "$RESPONSE_BODY" | jq -r '.end_time' | grep -q -v "null"; then
      END_TIME=$(echo "$RESPONSE_BODY" | jq -r '.end_time')
      echo -e "${BLUE}EndTime: ${END_TIME}${NC}"

      # 计算时长
      DURATION=$((END_TIME - START_TIME))
      echo -e "${BLUE}视频时长: ${DURATION}秒${NC}"
    else
      echo -e "${YELLOW}EndTime: 未设置${NC}"
    fi
  else
    echo -e "${BLUE}响应内容:${NC}"
    echo "$RESPONSE_BODY" | cat
    echo -e "${YELLOW}提示: 安装jq工具可以获得更好的结果展示${NC}"
  fi
else
  echo -e "${RED}导入失败! 状态码: ${HTTP_CODE}${NC}"
  echo -e "${RED}响应内容:${NC}"
  echo "$RESPONSE_BODY" | cat
fi

echo -e "${GREEN}响应已保存到: ${RESULT_FILE}${NC}"
echo -e "${BLUE}测试完成!${NC}"