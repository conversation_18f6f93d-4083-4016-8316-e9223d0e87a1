#!/bin/bash

# 测试OTA状态API的脚本
BASE_URL="http://localhost:5678/api"
DEVICE_ID="202503020224b6a6fac21000"

echo "==============================================="
echo "测试设备OTA状态管理API"
echo "==============================================="

echo "1. 测试获取设备OTA状态（设备不存在时会自动创建）"
curl -X GET "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" | jq .

echo -e "\n2. 测试更新设备OTA状态为'updating'"
curl -X PUT "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" \
  -d "{
    \"device_id\": \"${DEVICE_ID}\",
    \"status\": \"updating\"
  }" | jq .

echo -e "\n3. 再次获取设备OTA状态，验证更新是否成功"
curl -X GET "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" | jq .

echo -e "\n4. 测试更新设备OTA状态为'completed'"
curl -X PUT "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" \
  -d "{
    \"device_id\": \"${DEVICE_ID}\",
    \"status\": \"completed\"
  }" | jq .

echo -e "\n5. 测试更新设备OTA状态为'failed'"
curl -X PUT "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" \
  -d "{
    \"device_id\": \"${DEVICE_ID}\",
    \"status\": \"failed\"
  }" | jq .

echo -e "\n6. 测试更新设备OTA状态为'idle'"
curl -X PUT "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" \
  -d "{
    \"device_id\": \"${DEVICE_ID}\",
    \"status\": \"idle\"
  }" | jq .

echo -e "\n7. 测试无效的状态值"
curl -X PUT "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" \
  -d "{
    \"device_id\": \"${DEVICE_ID}\",
    \"status\": \"invalid_status\"
  }" | jq .

echo -e "\n8. 最终获取设备OTA状态"
curl -X GET "${BASE_URL}/devices/${DEVICE_ID}/ota-status" \
  -H "Content-Type: application/json" | jq .

echo -e "\n==============================================="
echo "OTA状态API测试完成"
echo "===============================================" 