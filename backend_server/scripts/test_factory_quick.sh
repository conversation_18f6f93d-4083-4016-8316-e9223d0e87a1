#!/bin/bash

# 工厂路由快速测试脚本
# 用于日常快速验证工厂API功能
# 使用方法: ./test_factory_quick.sh [server_url] [pcb_sn]

SERVER_URL=${1:-"http://localhost:5678"}
PCB_SN=${2:-"PCB_QUICK_TEST_$(date +%s)"}
API_BASE="$SERVER_URL/api/factory"

# 工厂永久Token
FACTORY_TOKEN="FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

echo "🚀 工厂API快速测试"
echo "服务器: $SERVER_URL"
echo "PCB SN: $PCB_SN"
echo "================================"

# 1. 健康检查
echo "1. 🔍 健康检查..."
HEALTH_RESULT=$(curl -s -X GET "$API_BASE/health")
echo "$HEALTH_RESULT" | jq '.'

if [[ $(echo "$HEALTH_RESULT" | jq -r '.status') != "ok" ]]; then
    echo "❌ 健康检查失败，退出测试"
    exit 1
fi
echo ""

# 2. 申请SN号
echo "2. 📝 申请SN号..."
APPLY_RESULT=$(curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN\",
    \"device_type\": \"toilet_v1\",
    \"production_line\": \"quick_test_line\",
    \"batch_number\": \"quick_test_batch\",
    \"operator\": \"快速测试员\",
    \"quantity\": 2,
    \"remark\": \"快速测试\"
  }")

echo "$APPLY_RESULT" | jq '.'

# 检查申请是否成功
if [[ $(echo "$APPLY_RESULT" | jq -r '.success') != "true" ]]; then
    echo "❌ SN号申请失败，退出测试"
    exit 1
fi

# 提取SN号
FIRST_SN=$(echo "$APPLY_RESULT" | jq -r '.data.sn_list[0]')
SECOND_SN=$(echo "$APPLY_RESULT" | jq -r '.data.sn_list[1]')
echo "生成的SN号: $FIRST_SN, $SECOND_SN"
echo ""

# 3. 检查SN号
echo "3. 🔍 检查SN号..."
CHECK_RESULT=$(curl -s -X GET "$API_BASE/sn/$FIRST_SN/check" \
  -H "Authorization: Bearer $FACTORY_TOKEN")
echo "$CHECK_RESULT" | jq '.'

if [[ $(echo "$CHECK_RESULT" | jq -r '.data.exists') != "true" ]]; then
    echo "❌ SN号检查失败"
    exit 1
fi
echo ""

# 4. 获取SN详情
echo "4. 📋 获取SN详情..."
DETAIL_RESULT=$(curl -s -X GET "$API_BASE/sn/$FIRST_SN" \
  -H "Authorization: Bearer $FACTORY_TOKEN")
echo "$DETAIL_RESULT" | jq '.'
echo ""

# 5. 更新SN状态
echo "5. 🔄 更新SN状态为已使用..."
UPDATE_RESULT=$(curl -s -X PUT "$API_BASE/sn/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"sn\": \"$FIRST_SN\",
    \"status\": 2,
    \"operator\": \"快速测试员\",
    \"remark\": \"快速测试使用\"
  }")

echo "$UPDATE_RESULT" | jq '.'

if [[ $(echo "$UPDATE_RESULT" | jq -r '.success') != "true" ]]; then
    echo "❌ SN状态更新失败"
    exit 1
fi
echo ""

# 6. 按PCB SN查询
echo "6. 🔍 按PCB SN查询..."
QUERY_RESULT=$(curl -s -X GET "$API_BASE/sn/query?pcb_sn=$PCB_SN&page=1&page_size=5" \
  -H "Authorization: Bearer $FACTORY_TOKEN")
echo "$QUERY_RESULT" | jq '.'
echo ""

# 7. 获取统计信息
echo "7. 📊 获取统计信息..."
STATS_RESULT=$(curl -s -X GET "$API_BASE/statistics" \
  -H "Authorization: Bearer $FACTORY_TOKEN")
echo "$STATS_RESULT" | jq '.'
echo ""

# 8. 验证SN号格式
echo "8. 🔍 验证生成的SN号格式..."
echo "第一个SN: $FIRST_SN"
echo "第二个SN: $SECOND_SN"

# 分解第一个SN号
IFS='_' read -ra SN_PARTS <<< "$FIRST_SN"
if [ ${#SN_PARTS[@]} -ge 3 ]; then
    RANDOM_HEX="${SN_PARTS[0]}"
    PCB_PART=$(echo "$FIRST_SN" | sed "s/^${RANDOM_HEX}_//; s/_[0-9]*$//")
    TIMESTAMP_PART="${SN_PARTS[-1]}"
    
    echo "  随机16进制: $RANDOM_HEX (长度: ${#RANDOM_HEX})"
    echo "  PCB SN: $PCB_PART"
    echo "  时间戳: $TIMESTAMP_PART (长度: ${#TIMESTAMP_PART})"
    
    # 验证格式
    if [ ${#RANDOM_HEX} -eq 8 ] && [ "$PCB_PART" = "$PCB_SN" ] && [ ${#TIMESTAMP_PART} -ge 13 ]; then
        echo "  ✅ SN号格式正确"
    else
        echo "  ❌ SN号格式错误"
        exit 1
    fi
else
    echo "  ❌ SN号格式错误，无法分解"
    exit 1
fi
echo ""

echo "================================"
echo "🎉 快速测试完成！所有功能正常"
echo ""
echo "📝 测试结果:"
echo "  ✅ 健康检查通过"
echo "  ✅ SN号申请成功"
echo "  ✅ SN号检查正常"
echo "  ✅ SN详情获取正常"
echo "  ✅ SN状态更新成功"
echo "  ✅ PCB SN查询正常"
echo "  ✅ 统计信息获取正常"
echo "  ✅ SN号格式验证通过"
echo ""
echo "🔑 使用的Token: FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
echo "📦 使用的PCB SN: $PCB_SN"
echo "🏷️  生成的SN号: $FIRST_SN, $SECOND_SN"
