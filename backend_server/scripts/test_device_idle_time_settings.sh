#!/bin/bash

# 测试设备闲时更新时间设置API
BASE_URL="http://localhost:5678"
DEVICE_ID="202502270220f7cbb4421000"

echo "=== 测试设备闲时更新时间设置API ==="

# 1. 获取设备所有设置
echo "1. 获取设备所有设置："
curl -X GET "${BASE_URL}/api/devices/${DEVICE_ID}/setting" \
  -H "Content-Type: application/json" | jq .

echo -e "\n"

# 2. 只更新自动OTA设置
echo "2. 只更新自动OTA设置为on："
curl -X PUT "${BASE_URL}/api/devices/${DEVICE_ID}/setting" \
  -H "Content-Type: application/json" \
  -d '{
    "auto_ota_upgrade": "on"
  }' | jq .

echo -e "\n"

# 3. 只更新闲时时间设置
echo "3. 只更新闲时时间设置（1-5点）："
curl -X PUT "${BASE_URL}/api/devices/${DEVICE_ID}/setting" \
  -H "Content-Type: application/json" \
  -d '{
    "idle_update_start_hour": 1,
    "idle_update_end_hour": 5
  }' | jq .

echo -e "\n"

# 4. 同时更新所有设置
echo "4. 同时更新所有设置："
curl -X PUT "${BASE_URL}/api/devices/${DEVICE_ID}/setting" \
  -H "Content-Type: application/json" \
  -d '{
    "auto_ota_upgrade": "off",
    "idle_update_start_hour": 3,
    "idle_update_end_hour": 6
  }' | jq .

echo -e "\n"

# 5. 验证最终设置
echo "5. 验证最终设置："
curl -X GET "${BASE_URL}/api/devices/${DEVICE_ID}/setting" \
  -H "Content-Type: application/json" | jq .

echo -e "\n"

# 6. 测试无效参数
echo "6. 测试无效的时间参数（25小时）："
curl -X PUT "${BASE_URL}/api/devices/${DEVICE_ID}/setting" \
  -H "Content-Type: application/json" \
  -d '{
    "idle_update_start_hour": 25
  }' | jq .

echo -e "\n"

# 7. 测试无效的OTA参数
echo "7. 测试无效的OTA参数："
curl -X PUT "${BASE_URL}/api/devices/${DEVICE_ID}/setting" \
  -H "Content-Type: application/json" \
  -d '{
    "auto_ota_upgrade": "invalid"
  }' | jq .

echo -e "\nAPI测试完成！" 