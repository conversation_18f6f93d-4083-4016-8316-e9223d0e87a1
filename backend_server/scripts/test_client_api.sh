#!/bin/bash

# 设置基础 URL
# BASE_URL="https://api.caby.care/api"
BASE_URL="http://localhost:5678/api"

# 设置测试用户ID
USER_ID="0220280ee0021000"

# 设置颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查必要的命令是否存在
command -v jq >/dev/null 2>&1 || { echo "需要安装 jq 命令行工具"; exit 1; }
command -v curl >/dev/null 2>&1 || { echo "需要安装 curl 命令行工具"; exit 1; }

# 检查服务器是否在运行
if ! curl -s "${BASE_URL}/health" > /dev/null; then
    echo "错误: 无法连接到服务器 ${BASE_URL}"
    exit 1
fi

# 辅助函数：打印请求结果
print_result() {
    # 检查 curl 是否成功
    if [ $1 -eq 0 ]; then
        # 尝试从响应中获取 code
        CODE=$(echo $3 | jq -r '.code // 0')
        if [ "$CODE" = "null" ]; then
            # 如果响应是数组，检查是否为空
            LENGTH=$(echo $3 | jq '. | length')
            if [ $LENGTH -gt 0 ]; then
                echo -e "${GREEN}✓ $2 成功${NC}"
            else
                echo -e "${RED}✗ $2 失败 (空数组)${NC}"
            fi
        elif [ $CODE -eq 200 ] || [ $CODE -eq 0 ]; then
            echo -e "${GREEN}✓ $2 成功${NC}"
        else
            echo -e "${RED}✗ $2 失败${NC}"
        fi
        echo "响应: $3"
    else
        echo -e "${RED}✗ $2 失败 (HTTP 错误)${NC}"
        echo "响应: $3"
    fi
    echo "----------------------------------------"
}

echo "开始测试客户端 API..."
echo "=========================================="

# 1. 注册新客户端
echo "1. 测试注册新客户端..."
# 先删除已存在的客户端（如果有）
DEVICE_ID="D57D7294-A00E-40BE-9992-06C32AD182E1"
curl -s -X DELETE "${BASE_URL}/clients/${DEVICE_ID}" > /dev/null

CLIENT_RESPONSE=$(curl -s -X POST "${BASE_URL}/clients/register" \
    -H "Content-Type: application/json" \
    -d '{
        "user_id": "'${USER_ID}'",
        "client_id": "'$(echo -n "D57D7294-A00E-40BE-9992-06C32AD182E1" | base64)'",
        "client_type": "ios",
        "name": "iPhone 14 Pro",
        "model": "iPhone15,2",
        "os_version": "17.0.1",
        "app_version": "1.0.0"
    }')
print_result $? "注册新客户端" "$CLIENT_RESPONSE"

# 提取客户端ID用于后续测试
CLIENT_ID=$(echo $CLIENT_RESPONSE | jq -r '.data.client_id')
if [ "$CLIENT_ID" = "null" ] || [ -z "$CLIENT_ID" ]; then
    # 如果获取失败，使用原始ID
    CLIENT_ID="D57D7294-A00E-40BE-9992-06C32AD182E1"
fi

# 2. 注册客户端令牌
echo "2. 测试注册客户端令牌..."
TOKEN_RESPONSE=$(curl -s -X POST "${BASE_URL}/clients/${CLIENT_ID}/token" \
    -H "Content-Type: application/json" \
    -d '{
        "client_token": "804d3ddcef43b62f1cc780a385ad699a8aa8c18d0a4666002316a15c066e6ad3",
        "token_type": "apns",
        "is_sandbox": false
    }')
print_result $? "注册客户端令牌" "$TOKEN_RESPONSE"

# 3. 获取客户端信息
echo "3. 测试获取客户端信息..."
CLIENT_INFO=$(curl -s -X GET "${BASE_URL}/clients/${CLIENT_ID}")
print_result $? "获取客户端信息" "$CLIENT_INFO"

# 4. 更新客户端信息
echo "4. 测试更新客户端信息..."
UPDATE_RESPONSE=$(curl -s -X PUT "${BASE_URL}/clients/${CLIENT_ID}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "我的iPhone",
        "app_version": "1.0.1"
    }')
print_result $? "更新客户端信息" "$UPDATE_RESPONSE"

# # 5. 获取客户端令牌信息
echo "5. 测试获取客户端令牌信息..."
TOKEN_INFO=$(curl -s -X GET "${BASE_URL}/clients/${CLIENT_ID}/token")
print_result $? "获取客户端令牌信息" "$TOKEN_INFO"

# 6. 发送客户端心跳
echo "6. 测试发送客户端心跳..."
HEARTBEAT_RESPONSE=$(curl -s -X POST "${BASE_URL}/clients/${CLIENT_ID}/heartbeat" \
    -H "Content-Type: application/json" \
    -d '{
        "app_version": "1.0.1",
        "status": 1
    }')
print_result $? "发送客户端心跳" "$HEARTBEAT_RESPONSE"

# 7. 更新客户端状态
echo "7. 测试更新客户端状态..."
STATUS_RESPONSE=$(curl -s -X PUT "${BASE_URL}/clients/${CLIENT_ID}/status" \
    -H "Content-Type: application/json" \
    -d '{
        "status": 1,
        "app_version": "1.0.1"
    }')
print_result $? "更新客户端状态" "$STATUS_RESPONSE"

# 8. 获取用户的所有客户端
echo "8. 测试获取用户的所有客户端..."
USER_CLIENTS=$(curl -s -X GET "${BASE_URL}/clients?user_id=${USER_ID}")
print_result $? "获取用户的所有客户端" "$USER_CLIENTS"

# 9. 删除客户端令牌
# echo "9. 测试删除客户端令牌..."
# DELETE_RESPONSE=$(curl -s -X DELETE "${BASE_URL}/clients/${CLIENT_ID}/token")
# print_result $? "删除客户端令牌" "$DELETE_RESPONSE"

echo "=========================================="
echo "测试完成"