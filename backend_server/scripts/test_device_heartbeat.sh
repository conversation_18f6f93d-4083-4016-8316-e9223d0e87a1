#!/bin/bash

# 设备心跳测试脚本
# 测试设备心跳API，包括信号强度、存储使用率、IP地址和固件版本更新
# 注意：固件版本现在直接更新到 Device 结构体中的 FirmwareVersion 字段

# 服务器地址
SERVER_URL="${SERVER_URL:-http://localhost:8080}"

# 设备ID（可以从环境变量获取，或使用默认值）
DEVICE_ID="${DEVICE_ID:-testdevice001}"

# API端点
HEARTBEAT_URL="$SERVER_URL/api/devices/heartbeat"

echo "=========================================="
echo "设备心跳测试脚本"
echo "服务器: $SERVER_URL"
echo "设备ID: $DEVICE_ID"
echo "固件版本更新: 直接更新 Device.FirmwareVersion"
echo "=========================================="

# 测试1: 基本心跳（只有设备ID）
echo "测试1: 基本心跳"
RESPONSE=$(curl -X POST "$HEARTBEAT_URL" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "device_id=$DEVICE_ID" \
     -w "\n状态码: %{http_code}" \
     -s)
echo "$RESPONSE"
echo "$RESPONSE" | grep -v "状态码:" | jq '.' 2>/dev/null || echo "原始响应: $(echo "$RESPONSE" | grep -v "状态码:")"

echo ""

# 测试2: 完整参数心跳（包括固件版本）
echo "测试2: 完整参数心跳（包括固件版本）"
RESPONSE=$(curl -X POST "$HEARTBEAT_URL" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "device_id=$DEVICE_ID&signal_strength=85&storage_usage=45&ipv4=*************&ipv6=2001:db8::1&firmware_version=1.2.3" \
     -w "\n状态码: %{http_code}" \
     -s)
echo "$RESPONSE"
echo "$RESPONSE" | grep -v "状态码:" | jq '.' 2>/dev/null || echo "原始响应: $(echo "$RESPONSE" | grep -v "状态码:")"

echo ""

# 测试3: 更新固件版本
echo "测试3: 更新固件版本到 2.0.0"
RESPONSE=$(curl -X POST "$HEARTBEAT_URL" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "device_id=$DEVICE_ID&signal_strength=90&storage_usage=50&firmware_version=2.0.0" \
     -w "\n状态码: %{http_code}" \
     -s)
echo "$RESPONSE"
echo "$RESPONSE" | grep -v "状态码:" | jq '.' 2>/dev/null || echo "原始响应: $(echo "$RESPONSE" | grep -v "状态码:")"

echo ""

# 测试4: 验证设备信息是否更新（获取设备状态）
echo "测试4: 验证设备状态更新"
RESPONSE=$(curl -X GET "$SERVER_URL/api/devices/$DEVICE_ID/status?user_id=testuser001" \
     -H "Content-Type: application/json" \
     -w "\n状态码: %{http_code}" \
     -s)
echo "$RESPONSE"
echo "$RESPONSE" | grep -v "状态码:" | jq '.' 2>/dev/null || echo "原始响应: $(echo "$RESPONSE" | grep -v "状态码:")"

echo ""
echo "=========================================="
echo "测试完成"
echo "=========================================="