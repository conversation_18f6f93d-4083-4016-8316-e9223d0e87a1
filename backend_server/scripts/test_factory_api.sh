#!/bin/bash

# 工厂API测试脚本
# 使用方法: ./test_factory_api.sh [server_url]

SERVER_URL=${1:-"http://localhost:5678"}
API_BASE="$SERVER_URL/api/factory"

echo "测试工厂API - 服务器: $SERVER_URL"
echo "=================================="

# 1. 测试健康检查
echo "1. 测试健康检查..."
curl -s -X GET "$API_BASE/health" | jq '.'
echo ""

# 2. 测试申请SN号
echo "2. 测试申请SN号..."
APPLY_RESPONSE=$(curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -d '{
    "device_type": "toilet_v1",
    "production_line": "line_001", 
    "batch_number": "batch_test_001",
    "operator": "测试员",
    "quantity": 5,
    "remark": "API测试批次"
  }')

echo "$APPLY_RESPONSE" | jq '.'

# 提取第一个SN号用于后续测试
FIRST_SN=$(echo "$APPLY_RESPONSE" | jq -r '.data.sn_list[0]')
echo "提取的SN号: $FIRST_SN"
echo ""

# 3. 测试检查SN是否存在
echo "3. 测试检查SN是否存在..."
curl -s -X GET "$API_BASE/sn/$FIRST_SN/check" | jq '.'
echo ""

# 4. 测试获取SN详细信息
echo "4. 测试获取SN详细信息..."
curl -s -X GET "$API_BASE/sn/$FIRST_SN" | jq '.'
echo ""

# 5. 测试更新SN状态为已使用
echo "5. 测试更新SN状态为已使用..."
curl -s -X PUT "$API_BASE/sn/update" \
  -H "Content-Type: application/json" \
  -d "{
    \"sn\": \"$FIRST_SN\",
    \"status\": 2,
    \"operator\": \"测试员\",
    \"remark\": \"测试使用\"
  }" | jq '.'
echo ""

# 6. 测试查询SN号
echo "6. 测试查询SN号..."
curl -s -X GET "$API_BASE/sn/query?batch_number=batch_test_001&page=1&page_size=10" | jq '.'
echo ""

# 7. 测试获取统计信息
echo "7. 测试获取统计信息..."
curl -s -X GET "$API_BASE/statistics" | jq '.'
echo ""

# 8. 测试错误情况 - 无效SN格式
echo "8. 测试错误情况 - 无效SN格式..."
curl -s -X GET "$API_BASE/sn/invalid_sn" | jq '.'
echo ""

# 9. 测试错误情况 - 申请过多SN号
echo "9. 测试错误情况 - 申请过多SN号..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -d '{
    "device_type": "toilet_v1",
    "quantity": 101
  }' | jq '.'
echo ""

echo "测试完成!"
