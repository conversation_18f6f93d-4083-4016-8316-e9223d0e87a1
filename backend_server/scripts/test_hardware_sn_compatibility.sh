#!/bin/bash

# 硬件SN号兼容性测试脚本
# 测试旧的8位16进制格式和新的复合格式兼容性
# 使用方法: ./test_hardware_sn_compatibility.sh [server_url]

SERVER_URL=${1:-"http://localhost:5678"}
API_BASE="$SERVER_URL/api"

# 工厂永久Token
FACTORY_TOKEN="FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

echo "测试硬件SN号兼容性 - 服务器: $SERVER_URL"
echo "测试旧8位16进制格式和新复合格式的兼容性"
echo "=================================================="

# 测试用户ID
TEST_USER_ID="test_user_001"

# 1. 测试健康检查
echo "1. 测试API健康检查..."
curl -s -X GET "$API_BASE/health" | jq '.'
echo ""

# 2. 首先从工厂API申请一个新的复合SN号
echo "2. 从工厂API申请新的复合SN号..."
PCB_SN="PCB_HARDWARE_TEST_001"
FACTORY_RESPONSE=$(curl -s -X POST "$API_BASE/factory/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN\",
    \"device_type\": \"toilet_v1\",
    \"production_line\": \"line_hardware_test\", 
    \"batch_number\": \"batch_hardware_test_001\",
    \"operator\": \"硬件测试员\",
    \"quantity\": 1,
    \"remark\": \"硬件SN兼容性测试\"
  }")

echo "$FACTORY_RESPONSE" | jq '.'

# 提取复合SN号
COMPOSITE_SN=$(echo "$FACTORY_RESPONSE" | jq -r '.data.sn_list[0]')
echo "生成的复合SN号: $COMPOSITE_SN"
echo ""

# 3. 测试添加复合格式的硬件SN号
echo "3. 测试添加复合格式的硬件SN号..."
COMPOSITE_RESPONSE=$(curl -s -X POST "$API_BASE/devices" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"hardware_sn\": \"$COMPOSITE_SN\",
    \"remark\": \"复合格式SN号测试\"
  }")

echo "$COMPOSITE_RESPONSE" | jq '.'
echo ""

# 4. 测试添加旧的8位16进制格式SN号
echo "4. 测试添加旧的8位16进制格式SN号..."
LEGACY_SN="A1B2C3D4"
LEGACY_RESPONSE=$(curl -s -X POST "$API_BASE/devices" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"hardware_sn\": \"$LEGACY_SN\",
    \"remark\": \"旧8位16进制格式SN号测试\"
  }")

echo "$LEGACY_RESPONSE" | jq '.'
echo ""

# 5. 测试查询用户硬件列表
echo "5. 测试查询用户硬件列表..."
curl -s -X GET "$API_BASE/devices/users/$TEST_USER_ID" | jq '.'
echo ""

# 6. 测试根据复合SN号查询硬件用户
echo "6. 测试根据复合SN号查询硬件用户..."
curl -s -X GET "$API_BASE/devices/hardware/$COMPOSITE_SN" | jq '.'
echo ""

# 7. 测试根据旧格式SN号查询硬件用户
echo "7. 测试根据旧格式SN号查询硬件用户..."
curl -s -X GET "$API_BASE/devices/hardware/$LEGACY_SN" | jq '.'
echo ""

# 8. 测试检查复合SN号是否存在
echo "8. 测试检查复合SN号是否存在..."
curl -s -X GET "$API_BASE/devices/check?user_id=$TEST_USER_ID&hardware_sn=$COMPOSITE_SN" | jq '.'
echo ""

# 9. 测试检查旧格式SN号是否存在
echo "9. 测试检查旧格式SN号是否存在..."
curl -s -X GET "$API_BASE/devices/check?user_id=$TEST_USER_ID&hardware_sn=$LEGACY_SN" | jq '.'
echo ""

# 10. 测试错误格式的SN号
echo "10. 测试错误格式的SN号..."

# 10.1 测试空SN号
echo "10.1 测试空SN号（应该失败）..."
curl -s -X POST "$API_BASE/devices" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"hardware_sn\": \"\",
    \"remark\": \"空SN号测试\"
  }" | jq '.'
echo ""

# 10.2 测试格式错误的复合SN号
echo "10.2 测试格式错误的复合SN号（应该失败）..."
curl -s -X POST "$API_BASE/devices" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"hardware_sn\": \"INVALID_FORMAT\",
    \"remark\": \"错误格式SN号测试\"
  }" | jq '.'
echo ""

# 10.3 测试复合SN号缺少部分
echo "10.3 测试复合SN号缺少部分（应该失败）..."
curl -s -X POST "$API_BASE/devices" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"hardware_sn\": \"A1B2C3D4_PCB123\",
    \"remark\": \"缺少时间戳的复合SN号测试\"
  }" | jq '.'
echo ""

# 10.4 测试旧格式但长度错误的SN号
echo "10.4 测试旧格式但长度错误的SN号（应该失败）..."
curl -s -X POST "$API_BASE/devices" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"hardware_sn\": \"A1B2C3\",
    \"remark\": \"长度错误的旧格式SN号测试\"
  }" | jq '.'
echo ""

echo "=================================================="
echo "硬件SN号兼容性测试完成!"
echo ""
echo "📝 测试总结:"
echo "✅ 支持的SN号格式:"
echo "  1. 新复合格式: 8位随机16进制_PCB_SN_时间戳"
echo "     示例: A1B2C3D4_PCB001234567_1737012345678"
echo "  2. 旧8位16进制格式: 8位16进制字符"
echo "     示例: A1B2C3D4"
echo "  3. 其他格式: 只要非空即可（向前兼容）"
echo ""
echo "❌ 不支持的格式:"
echo "  1. 空SN号"
echo "  2. 复合格式但缺少部分（必须有3个部分）"
echo "  3. 复合格式但随机16进制部分不是8位"
echo "  4. 复合格式但时间戳部分不是数字"
echo ""
echo "🔧 API端点测试:"
echo "  POST   /api/devices                    - 添加用户硬件关联"
echo "  GET    /api/devices/users/{user_id}    - 查询用户硬件列表"
echo "  GET    /api/devices/hardware/{sn}      - 根据SN查询硬件用户"
echo "  GET    /api/devices/check              - 检查用户硬件关联是否存在"
