#!/bin/bash

# 设备传感器状态API测试脚本
# 作者: AI Assistant
# 用途: 测试设备传感器错误上报、状态查询和清除功能

# 设置API基础URL（请根据实际情况修改）
BASE_URL="http://localhost:5678/api"

# 测试设备ID（请根据实际情况修改）
DEVICE_ID="202503020224b6a6fac21000"
USER_ID="022b605dc3421000"

echo "=========================================="
echo "设备传感器状态API测试脚本"
echo "=========================================="
echo ""

# 1. 上报相机传感器错误
echo "1. 上报相机传感器错误..."
curl -X POST "$BASE_URL/devices/$DEVICE_ID/sensor-errors" \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "'$DEVICE_ID'",
    "sensor_type": "camera",
    "error_code": 1001,
    "error_message": "Camera initialization failed",
    "additional_info": "Camera module not responding"
  }' \
  -w "\nHTTP状态码: %{http_code}\n\n"

# 2. 上报重量传感器错误
echo "2. 上报重量传感器错误..."
curl -X POST "$BASE_URL/devices/$DEVICE_ID/sensor-errors" \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "'$DEVICE_ID'",
    "sensor_type": "weight_sensor",
    "error_code": 2001,
    "error_message": "Weight sensor calibration error",
    "additional_info": "Sensor readings out of range"
  }' \
  -w "\nHTTP状态码: %{http_code}\n\n"

# 3. 上报WiFi连接错误
echo "3. 上报WiFi连接错误..."
curl -X POST "$BASE_URL/devices/$DEVICE_ID/sensor-errors" \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "'$DEVICE_ID'",
    "sensor_type": "wifi",
    "error_code": 3001,
    "error_message": "WiFi connection lost",
    "additional_info": "Unable to connect to configured network"
  }' \
  -w "\nHTTP状态码: %{http_code}\n\n"

# 4. 查询设备传感器状态
echo "4. 查询设备传感器状态..."
curl -X GET "$BASE_URL/devices/$DEVICE_ID/sensor-status?user_id=$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP状态码: %{http_code}\n\n"

# 5. 获取所有有错误的设备列表
echo "5. 获取所有有传感器错误的设备列表..."
curl -X GET "$BASE_URL/devices/sensor-errors" \
  -H "Content-Type: application/json" \
  -w "\nHTTP状态码: %{http_code}\n\n"

# 6. 清除相机传感器错误状态
echo "6. 清除相机传感器错误状态..."
curl -X DELETE "$BASE_URL/devices/$DEVICE_ID/sensor-errors/camera?user_id=$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP状态码: %{http_code}\n\n"

# 7. 再次查询设备传感器状态（验证清除效果）
echo "7. 再次查询设备传感器状态（验证清除效果）..."
curl -X GET "$BASE_URL/devices/$DEVICE_ID/sensor-status?user_id=$USER_ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP状态码: %{http_code}\n\n"

echo "=========================================="
echo "测试完成！"
echo "=========================================="
echo ""
echo "测试说明："
echo "1. 上报了相机、重量传感器和WiFi的错误"
echo "2. 查询了设备的传感器状态"
echo "3. 清除了相机传感器的错误状态"
echo "4. 验证了清除效果"
echo ""
echo "注意："
echo "- 请确保已启动后端服务"
echo "- 请根据实际情况修改DEVICE_ID和USER_ID"
echo "- 请确保测试的设备ID在数据库中存在"
echo "" 