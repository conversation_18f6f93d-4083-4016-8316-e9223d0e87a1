#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# API 基础URL
BASE_URL="http://***************:5678/api"
# BASE_URL="http://localhost:5678/api"

# 测试数据
USER_ID="022b605dc3421000"
DEVICE_ID="202502270220f7cbb4421000"
VIDEO_ID=""

echo "开始测试视频处理流程..."
echo "用户ID: $USER_ID"
echo "设备ID: $DEVICE_ID"

# 生成随机持续时间（10-120秒之间）
DURATION=$((10 + RANDOM % 111))
echo "视频持续时间: ${DURATION}秒"

# 获取当前时间的时间戳（秒）
CURRENT_TIMESTAMP=$(date +%s)
END_TIMESTAMP=$((CURRENT_TIMESTAMP + DURATION))

# 格式化为ISO8601/RFC3339格式
format_timestamp() {
  local timestamp=$1
  local formatted
  local timezone

  if command -v date >/dev/null 2>&1; then
    if date -r $timestamp +"%Y-%m-%dT%H:%M:%S" >/dev/null 2>&1; then
      # macOS 方式
      formatted=$(date -r $timestamp +"%Y-%m-%dT%H:%M:%S")
    elif date -d "@$timestamp" +"%Y-%m-%dT%H:%M:%S" >/dev/null 2>&1; then
      # Linux 方式
      formatted=$(date -d "@$timestamp" +"%Y-%m-%dT%H:%M:%S")
    else
      # 通用方式
      formatted=$(date +"%Y-%m-%dT%H:%M:%S")
    fi
  fi

  # 获取系统时区，并插入冒号以符合 RFC3339 格式
  timezone=$(date +%z)
  # 将 +0800 转换为 +08:00 格式 (如果不为空)
  if [ -n "$timezone" ] && [ ${#timezone} -eq 5 ]; then
    timezone=${timezone:0:3}:${timezone:3:2}
  fi

  # 输出完整的RFC3339格式时间
  echo "${formatted}${timezone}"
}

# 格式化时间戳
START_TIME=$(format_timestamp $CURRENT_TIMESTAMP)
END_TIME=$(format_timestamp $END_TIMESTAMP)

echo "开始时间: $START_TIME"
echo "结束时间: $END_TIME"

request_data="{
        \"user_id\": \"$USER_ID\",
        \"record\": {
            \"device_id\": \"$DEVICE_ID\",
            \"start_time\": $CURRENT_TIMESTAMP,
            \"end_time\": $END_TIMESTAMP,
            \"weight_litter\": 4.5,
            \"weight_cat\": 4.0,
            \"weight_waste\": 0.3
        }
    }"

# 1. Create a video record
# echo -e "\n${GREEN}1. 创建视频记录${NC}"
# VIDEO_RECORD_RESPONSE=$(curl -s -X POST "${BASE_URL}/records" \
#   -H "Content-Type: application/json" \
#   -d "{
#     \"user_id\": \"$USER_ID\",
#     \"record\": {
#         \"device_id\": \"$DEVICE_ID\",
#         \"start_time\": $CURRENT_TIMESTAMP,
#         \"end_time\": $END_TIMESTAMP,
#         \"weight_litter\": 4.5,
#         \"weight_cat\": 4.8,
#         \"weight_waste\": 0.3
#     }
#   }")

echo "请求数据: $request_data"

VIDEO_RECORD_RESPONSE=$(curl -s -X POST "${BASE_URL}/records" \
  -H "Content-Type: application/json" \
  -d "$request_data")

echo "视频记录响应: $VIDEO_RECORD_RESPONSE"
#   {"user_id":"0220280ee0021000","record":{"video_id":"v160287367175999488","device_id":"202502270220f7cbb4421000","start_time":"2025-03-18T07:24:45Z","end_time":"2025-03-18T07:24:45Z","video_path":"","file_size":0,"format":"","status":1,"process_stage":0,"weight_litter":4.5,"weight_cat":4.8,"weight_waste":0.3,"created_at":"2025-03-18T15:24:46.330913+08:00","updated_at":"2025-03-18T15:24:46.330913+08:00"}}
VIDEO_ID=$(echo $VIDEO_RECORD_RESPONSE | jq -r '.record.video_id')
if [ -z "$VIDEO_ID" ]; then
    echo -e "${RED}创建视频记录失败${NC}"
    exit 1
fi
echo "视频ID: $VIDEO_ID"

echo -e "\n${GREEN}测试完成${NC}"

