#!/bin/bash

# Script to test the DeviceTimezone endpoint

# Default values
SERVER_URL="http://localhost:8080"
DEVICE_ID=""
TIMEZONE=""

# Display usage information
function show_usage {
    echo "Usage: $0 -d <device_id> -t <timezone> [-s <server_url>]"
    echo "  -d  Device ID (required)"
    echo "  -t  Timezone (required, e.g., 'Asia/Shanghai')"
    echo "  -s  Server URL (default: http://localhost:8080)"
    echo "  -h  Display this help message"
    exit 1
}

# Parse command line arguments
while getopts "d:t:s:h" opt; do
    case $opt in
        d) DEVICE_ID="$OPTARG" ;;
        t) TIMEZONE="$OPTARG" ;;
        s) SERVER_URL="$OPTARG" ;;
        h) show_usage ;;
        *) show_usage ;;
    esac
done

# Check for required parameters
if [ -z "$DEVICE_ID" ] || [ -z "$TIMEZONE" ]; then
    echo "Error: Device ID and timezone are required."
    show_usage
fi

# Display test information
echo "Testing DeviceTimezone endpoint"
echo "Server URL: $SERVER_URL"
echo "Device ID: $DEVICE_ID"
echo "Timezone: $TIMEZONE"

# Make the request
echo -e "\nSending request..."
RESPONSE=$(curl -s -w "\nHTTP Status: %{http_code}" -X POST \
    -d "device_id=$DEVICE_ID" \
    -d "timezone=$TIMEZONE" \
    "$SERVER_URL/api/devices/timezone")

# Extract the HTTP status code
HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP Status:" | cut -d' ' -f3)
RESPONSE_BODY=$(echo "$RESPONSE" | grep -v "HTTP Status:")

echo -e "\nResponse:"
echo "$RESPONSE_BODY"
echo ""

# Check if the request was successful
if [ "$HTTP_STATUS" -eq 200 ]; then
    echo "✅ Test passed! Device timezone updated successfully."
else
    echo "❌ Test failed! HTTP Status: $HTTP_STATUS"
fi
