#!/bin/bash

# 测试创建猫咪API的脚本
# 服务器地址
BASE_URL="http://localhost:5678"

# 颜色输出函数
print_success() {
    echo -e "\033[32m✓ $1\033[0m"
}

print_error() {
    echo -e "\033[31m✗ $1\033[0m"
}

print_info() {
    echo -e "\033[34mℹ $1\033[0m"
}

print_header() {
    echo -e "\033[36m=== $1 ===\033[0m"
}

# 获取访问令牌的函数（需要根据实际的认证流程调整）
get_access_token() {
    # 这里需要根据实际的登录流程获取token
    # 临时使用测试token，实际使用时需要替换
    echo "test_access_token"
}

# 执行HTTP请求的函数
make_request() {
    local method=$1
    local url=$2
    local data=$3
    local token=$4
    
    if [ -n "$token" ]; then
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             -H "Authorization: Bearer $token" \
             -d "$data" \
             "$url"
    else
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "$url"
    fi
}

# 测试案例函数
test_create_cat_success() {
    print_header "测试创建猫咪 - 成功案例"
    
    local token=$(get_access_token)
    
    # 测试1: 完整信息的雄性已绝育猫咪
    print_info "测试1: 创建完整信息的雄性已绝育猫咪"
    local data1='{
        "name": "Whiskers",
        "gender": 10,
        "birthday": "2020-03-15",
        "weight": 4.5,
        "breed": "British Shorthair",
        "color": "Orange Tabby",
        "photos_base64": ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="]
    }'
    
    local response1=$(make_request "POST" "$BASE_URL/api/cats" "$data1" "$token")
    echo "Response: $response1"
    
    if echo "$response1" | grep -q '"status":"success"'; then
        print_success "测试1通过"
    else
        print_error "测试1失败"
    fi
    
    # 测试2: 最小信息的雌性未知绝育状态猫咪
    print_info "测试2: 创建最小信息的雌性未知绝育状态猫咪"
    local data2='{
        "name": "Luna",
        "gender": -1
    }'
    
    local response2=$(make_request "POST" "$BASE_URL/api/cats" "$data2" "$token")
    echo "Response: $response2"
    
    if echo "$response2" | grep -q '"status":"success"'; then
        print_success "测试2通过"
    else
        print_error "测试2失败"
    fi
    
    # 测试3: 性别未知的猫咪
    print_info "测试3: 创建性别未知的猫咪"
    local data3='{
        "name": "Mystery Cat",
        "gender": 0,
        "birthday": "2021-01-01"
    }'
    
    local response3=$(make_request "POST" "$BASE_URL/api/cats" "$data3" "$token")
    echo "Response: $response3"
    
    if echo "$response3" | grep -q '"status":"success"'; then
        print_success "测试3通过"
    else
        print_error "测试3失败"
    fi
}

test_create_cat_validation() {
    print_header "测试创建猫咪 - 验证错误案例"
    
    local token=$(get_access_token)
    
    # 测试4: 缺少必填字段name
    print_info "测试4: 缺少必填字段name"
    local data4='{
        "gender": 1
    }'
    
    local response4=$(make_request "POST" "$BASE_URL/api/cats" "$data4" "$token")
    echo "Response: $response4"
    
    if echo "$response4" | grep -q '"error"'; then
        print_success "测试4通过 - 正确返回错误"
    else
        print_error "测试4失败 - 应该返回错误"
    fi
    
    # 测试5: 无效的gender编码
    print_info "测试5: 无效的gender编码"
    local data5='{
        "name": "Invalid Gender Cat",
        "gender": 99
    }'
    
    local response5=$(make_request "POST" "$BASE_URL/api/cats" "$data5" "$token")
    echo "Response: $response5"
    
    if echo "$response5" | grep -q '"error".*Invalid gender code'; then
        print_success "测试5通过 - 正确返回gender验证错误"
    else
        print_error "测试5失败 - 应该返回gender验证错误"
    fi
    
    # 测试6: 无效的生日格式
    print_info "测试6: 无效的生日格式"
    local data6='{
        "name": "Invalid Birthday Cat",
        "gender": 1,
        "birthday": "invalid-date"
    }'
    
    local response6=$(make_request "POST" "$BASE_URL/api/cats" "$data6" "$token")
    echo "Response: $response6"
    
    if echo "$response6" | grep -q '"error".*Invalid birthday format'; then
        print_success "测试6通过 - 正确返回生日格式错误"
    else
        print_error "测试6失败 - 应该返回生日格式错误"
    fi
    
    # 测试7: 未授权访问
    print_info "测试7: 未授权访问"
    local data7='{
        "name": "Unauthorized Cat",
        "gender": 1
    }'
    
    local response7=$(make_request "POST" "$BASE_URL/api/cats" "$data7" "")
    echo "Response: $response7"
    
    if echo "$response7" | grep -q '"error".*Authentication required'; then
        print_success "测试7通过 - 正确返回认证错误"
    else
        print_error "测试7失败 - 应该返回认证错误"
    fi
}

test_gender_codes() {
    print_header "测试所有性别编码"
    
    local token=$(get_access_token)
    
    # 定义所有有效的gender编码
    declare -A gender_codes=(
        [0]="性别未知"
        [1]="雄性未知绝育状态"
        [-1]="雌性未知绝育状态"
        [10]="雄性已绝育"
        [11]="雄性未绝育"
        [-10]="雌性已绝育"
        [-11]="雌性未绝育"
    )
    
    for code in "${!gender_codes[@]}"; do
        print_info "测试性别编码: $code (${gender_codes[$code]})"
        
        local data="{
            \"name\": \"Test Cat $code\",
            \"gender\": $code
        }"
        
        local response=$(make_request "POST" "$BASE_URL/api/cats" "$data" "$token")
        echo "Response: $response"
        
        if echo "$response" | grep -q '"status":"success"'; then
            print_success "性别编码 $code 测试通过"
        else
            print_error "性别编码 $code 测试失败"
        fi
    done
}

# 主函数
main() {
    print_header "开始测试创建猫咪API"
    
    # 检查服务器是否运行
    if ! curl -s "$BASE_URL/api/health" > /dev/null; then
        print_error "服务器无法访问，请确保服务器在 $BASE_URL 运行"
        exit 1
    fi
    
    print_success "服务器连接正常"
    
    # 运行测试
    test_create_cat_success
    echo
    test_create_cat_validation
    echo
    test_gender_codes
    
    print_header "测试完成"
}

# 运行主函数
main "$@" 