#!/bin/bash

# 测试设备设置API（从device_all_settings表获取）

SERVER_URL="http://localhost:5678"
DEVICE_ID="202505160285582a54021000"  # 替换为实际设备ID

# 测试心跳响应（查看是否包含auto_ota_upgrade字段）
echo "Testing device heartbeat..."
curl -X POST $SERVER_URL/api/devices/heartbeat \
  -F "device_id=$DEVICE_ID" \
  -F "signal_strength=85" \
  -F "ipv4=*************" \
  -F "storage_usage=30"
echo -e "\n"

# 获取设备设置
echo "Getting device settings..."
curl -X GET $SERVER_URL/api/devices/$DEVICE_ID/setting
echo -e "\n"

# 更新设备自动OTA更新设置为启用
echo "Updating device OTA upgrade setting to 'on'..."
curl -X PUT $SERVER_URL/api/devices/$DEVICE_ID/setting \
  -H "Content-Type: application/json" \
  -d '{
    "auto_ota_upgrade": "on"
  }'
echo -e "\n"

# 再次获取设置，确认更新成功
echo "Verifying update..."
curl -X GET $SERVER_URL/api/devices/$DEVICE_ID/setting
echo -e "\n"

# 再次测试心跳，确认返回更新后的auto_ota_upgrade值
echo "Testing heartbeat response with updated setting..."
curl -X POST $SERVER_URL/api/devices/heartbeat \
  -F "device_id=$DEVICE_ID" \
  -F "signal_strength=85" \
  -F "ipv4=*************" \
  -F "storage_usage=30"
echo -e "\n"

# # 更新设备自动OTA更新设置为禁用
# echo "Updating device OTA upgrade setting back to 'off'..."
# curl -X PUT $SERVER_URL/api/devices/$DEVICE_ID/setting \
#   -H "Content-Type: application/json" \
#   -d '{
#     "auto_ota_upgrade": "off"
#   }'
# echo -e "\n"

# 最终确认设置
echo "Final verification..."
curl -X GET $SERVER_URL/api/devices/$DEVICE_ID/auto-ota
echo -e "\n"
