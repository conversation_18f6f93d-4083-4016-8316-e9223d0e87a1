# 创建猫咪 API 测试指南

本文档说明如何测试新的创建猫咪 API 接口，包括完整的 Logto 认证流程。

## 📋 前置要求

### 系统要求
- macOS/Linux/WSL
- bash shell
- curl
- jq (用于 JSON 解析)

### 安装 jq
```bash
# macOS
brew install jq

# Ubuntu/Debian
sudo apt-get install jq

# CentOS/RHEL
sudo yum install jq
```

### 服务器要求
- 后端服务器运行在 `localhost:5678`
- Logto 认证服务配置正确

## 🚀 测试脚本概览

我们提供了多个测试脚本以适应不同的测试需求：

| 脚本文件 | 用途 | 认证要求 |
|---------|------|----------|
| `test_create_cat_with_auth.sh` | **完整测试（推荐）** | 集成 Logto 认证 |
| `test_create_cat_with_env.sh` | 快速测试 | 使用环境变量 |
| `test_create_cat_simple.sh` | 简单测试 | 无认证（仅用于调试） |
| `test_create_cat.sh` | 基础测试模板 | 需要手动提供 token |
| `setup_test_env.sh` | 环境设置 | 辅助脚本 |

## 🔐 认证方式

### 方式 1: 完整 Logto 认证流程（推荐）

这是最完整和真实的测试方式：

```bash
cd backend_server/scripts
./test_create_cat_with_auth.sh
```

**流程说明：**
1. 脚本会检查是否有现有的 token 文件
2. 如果没有，会生成 Logto 授权 URL
3. 用户在浏览器中完成登录
4. 复制授权码返回到脚本
5. 脚本获取 access token 并开始测试

**首次运行：**
```bash
# 1. 脚本会显示类似这样的 URL：
# https://brx8db.logto.app/oidc/auth?client_id=...

# 2. 在浏览器中打开 URL 并登录

# 3. 登录后会重定向到包含 code 参数的 URL，例如：
# http://localhost:5678/api/callback?code=ABC123...&state=XYZ

# 4. 复制 code 参数的值（ABC123...）粘贴到脚本中
```

### 方式 2: 使用环境变量（快速测试）

如果你已经有了有效的 token，可以使用这种方式：

```bash
# 设置环境
./setup_test_env.sh

# 运行测试
./test_create_cat_with_env.sh
```

### 方式 3: 手动设置 token

```bash
# 导出 token
export ACCESS_TOKEN="your_access_token_here"

# 运行测试
./test_create_cat_with_env.sh
```

## 📁 文件说明

### Token 文件
- `.logto_tokens.json` - 完整的 token 信息（JSON 格式）
- `logto_env.sh` - 环境变量格式的 token 信息

### 测试详细说明

#### `test_create_cat_with_auth.sh`
**功能：**
- 完整的 Logto OAuth2 认证流程
- Token 自动管理（获取、刷新、存储）
- 全面的 API 测试覆盖

**测试内容：**
- 用户认证验证
- 成功案例测试（3个）
- 验证错误案例（3个）  
- 所有性别编码测试（7个）
- 用户猫咪列表获取

#### `test_create_cat_with_env.sh`
**功能：**
- 使用预设的环境变量进行认证
- 快速执行所有测试案例
- 自动尝试加载现有 token

**适用场景：**
- 开发过程中的快速验证
- CI/CD 管道中的自动化测试
- 重复测试时避免重新认证

#### `setup_test_env.sh`
**功能：**
- 检测并加载现有的 token 文件
- 创建环境变量文件
- 提供手动测试命令

## 🧪 测试案例

### 成功案例

1. **完整信息的雄性已绝育猫咪**
```json
{
  "name": "Whiskers",
  "gender": 10,
  "birthday": "2020-03-15",
  "weight": 4.5,
  "breed": "British Shorthair",
  "color": "Orange Tabby"
}
```

2. **最小信息的雌性未知绝育状态猫咪**
```json
{
  "name": "Luna",
  "gender": -1
}
```

3. **性别未知的猫咪**
```json
{
  "name": "Mystery Cat",
  "gender": 0,
  "birthday": "2021-01-01"
}
```

### 验证错误案例

1. **缺少必填字段** - 应返回 400 错误
2. **无效性别编码** - 应返回 400 错误和详细说明
3. **无效生日格式** - 应返回 400 错误

### 性别编码测试

测试所有 7 个有效的性别编码：

| 编码 | 含义 |
|------|------|
| 0 | 性别未知 |
| 1 | 雄性，绝育状态未知 |
| -1 | 雌性，绝育状态未知 |
| 10 | 雄性，已绝育 |
| 11 | 雄性，未绝育 |
| -10 | 雌性，已绝育 |
| -11 | 雌性，未绝育 |

## 🔧 故障排除

### 常见问题

#### 1. "服务器无法访问"
```bash
# 检查服务器是否运行
curl http://localhost:5678/api/health

# 启动服务器
cd backend_server
go run main.go
```

#### 2. "需要安装 jq"
```bash
# macOS
brew install jq

# Linux
sudo apt-get install jq
```

#### 3. "未找到 ACCESS_TOKEN 环境变量"
```bash
# 运行完整认证流程
./test_create_cat_with_auth.sh

# 或设置环境
./setup_test_env.sh
```

#### 4. "获取 token 失败"
- 检查 Logto 服务器是否可用
- 确认客户端 ID 和重定向 URI 配置正确
- 检查网络连接

#### 5. "用户认证失败"
- Token 可能已过期，尝试刷新：
```bash
# 选择刷新选项
./test_create_cat_with_auth.sh
# 选择 "2) 尝试刷新token"
```

### Debug 模式

如果需要查看详细的请求/响应信息，可以在脚本中添加 `-v` 选项到 curl 命令：

```bash
# 编辑脚本，找到 curl 命令，添加 -v 选项
curl -v -s -X POST ...
```

## 📊 预期输出

### 成功的测试输出示例

```
=== 带Logto认证的创建猫咪API测试 ===
✓ 服务器连接正常

=== 获取访问令牌 ===
[INFO] 使用现有token继续...
[SUCCESS] Access Token: eyJhbGciOiJSUzI1NiIs...
[INFO] User ID: user_123456789

=== 验证用户认证 ===
用户信息: {"user_id":"user_123456789","email":"<EMAIL>"...}
✓ 用户认证成功

=== 测试创建猫咪 - 成功案例 ===
ℹ 测试1: 创建完整信息的雄性已绝育猫咪
Response: {"status":"success","message":"Cat created successfully","data":{"cat_id":"c_1234567890123456"...}}
✓ 测试1通过

...

=== 测试完成 ===
```

## 🎯 快速开始

**第一次测试：**
```bash
cd backend_server/scripts
chmod +x *.sh
./test_create_cat_with_auth.sh
```

**后续测试：**
```bash
./test_create_cat_with_env.sh
```

**手动单个测试：**
```bash
source logto_env.sh
curl -X POST "http://localhost:5678/api/cats" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $LOGTO_ACCESS_TOKEN" \
  -d '{"name": "Test Cat", "gender": 1}'
```

这样你就可以全面测试创建猫咪的 API 了！🐱 