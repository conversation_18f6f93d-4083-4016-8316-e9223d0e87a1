#!/bin/bash

# 彩色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认参数
SERVER_URL=${1:-"http://144.126.146.223:5678"}
DEVICE_ID=${2:-"202502270220f7cbb4421000"}
USER_ID=${3:-"022b605dc3421000"}
INPUT_FILE=${4:-"object_paths.txt"}

# 检查输入文件
if [ ! -f "$INPUT_FILE" ]; then
  echo -e "${RED}错误: 输入文件 ${INPUT_FILE} 不存在${NC}"
  echo -e "用法: $0 [服务器URL] [设备ID] [用户ID] [输入文件]"
  echo -e "示例: $0 http://144.126.146.223:5678 202502270220f7cbb4421000 022b605dc3421000 object_paths.txt"
  exit 1
fi

# 显示参数
echo -e "${BLUE}开始批量导入视频记录...${NC}"
echo -e "${BLUE}服务器地址: ${SERVER_URL}${NC}"
echo -e "${BLUE}设备ID: ${DEVICE_ID}${NC}"
echo -e "${BLUE}用户ID: ${USER_ID}${NC}"
echo -e "${BLUE}输入文件: ${INPUT_FILE}${NC}"

# 读取输入文件
OBJECT_PATHS=()
while IFS= read -r line || [[ -n "$line" ]]; do
  # 忽略空行和注释行
  if [[ -n "$line" && ! "$line" =~ ^# ]]; then
    OBJECT_PATHS+=("$line")
  fi
done < "$INPUT_FILE"

# 确认处理
echo -e "${YELLOW}将处理 ${#OBJECT_PATHS[@]} 个对象路径。继续? [y/N]${NC}"
read -r confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
  echo -e "${RED}已取消操作${NC}"
  exit 0
fi

# 准备JSON请求体
JSON_REQUEST=$(cat <<EOF
{
  "object_paths": $(printf '%s\n' "${OBJECT_PATHS[@]}" | jq -R . | jq -s .),
  "device_id": "$DEVICE_ID",
  "user_id": "$USER_ID"
}
EOF
)

# 调用API
echo -e "${GREEN}发送批量导入请求...${NC}"
RESPONSE=$(curl -s -X POST "${SERVER_URL}/api/temp/batch-import-records" \
  -H "Content-Type: application/json" \
  -d "$JSON_REQUEST")

# 保存响应到文件
TIMESTAMP=$(date +%Y%m%d%H%M%S)
RESULT_FILE="import_result_${TIMESTAMP}.json"
echo "$RESPONSE" > "$RESULT_FILE"

# 解析结果
if command -v jq &> /dev/null; then
  TOTAL=$(echo "$RESPONSE" | jq -r '.total // 0')
  IMPORTED=$(echo "$RESPONSE" | jq -r '.imported // 0')
  FAILED=$(echo "$RESPONSE" | jq -r '.failed // 0')

  echo -e "${GREEN}导入完成!${NC}"
  echo -e "${BLUE}总记录数: ${TOTAL}${NC}"
  echo -e "${GREEN}成功导入: ${IMPORTED}${NC}"

  if [ "$FAILED" -gt 0 ]; then
    echo -e "${RED}导入失败: ${FAILED}${NC}"
    echo -e "${RED}查看 ${RESULT_FILE} 获取失败详情${NC}"
  fi
else
  echo -e "${GREEN}导入完成!${NC}"
  echo -e "${BLUE}详细结果已保存到 ${RESULT_FILE}${NC}"
  echo -e "${RED}提示: 安装jq工具可以获得更好的结果展示${NC}"
fi

echo -e "${BLUE}完成!${NC}"