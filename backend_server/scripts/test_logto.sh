#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # 无颜色

# 配置参数
API_BASE="https://api.caby.care"
LOGTO_BASE="https://brx8db.logto.app"
CLIENT_ID="m5e89cx7qhnv16ofchw9u"
REDIRECT_URI="https://api.caby.care/api/callback"
LOG_FILE="logto_api_test_$(date +%Y%m%d_%H%M%S).log"

# 初始化日志文件
echo "Logto API 测试日志 - $(date)" > $LOG_FILE
echo "=================================" >> $LOG_FILE
echo "API基础URL: $API_BASE" >> $LOG_FILE
echo "Logto基础URL: $LOGTO_BASE" >> $LOG_FILE
echo "客户端ID: $CLIENT_ID" >> $LOG_FILE
echo "重定向URI: $REDIRECT_URI" >> $LOG_FILE
echo "=================================" >> $LOG_FILE

# 日志函数
log() {
    local level=$1
    local message=$2
    local color=$NC
    
    case $level in
        "INFO") color=$BLUE ;;
        "SUCCESS") color=$GREEN ;;
        "WARNING") color=$YELLOW ;;
        "ERROR") color=$RED ;;
        "DEBUG") color=$CYAN ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] [$level] $message" >> $LOG_FILE
}

# 保存令牌的函数
save_tokens() {
    local response=$1
    echo "$response" > .logto_tokens.json
    log "SUCCESS" "令牌已保存到 .logto_tokens.json"
    
    # 提取access_token用于后续测试
    ACCESS_TOKEN=$(echo "$response" | jq -r '.access_token')
    REFRESH_TOKEN=$(echo "$response" | jq -r '.refresh_token')
    ID_TOKEN=$(echo "$response" | jq -r '.id_token')
    USER_ID=$(echo "$response" | jq -r '.user_id')
    LOGTO_ID=$(echo "$response" | jq -r '.logto_id')
    
    echo "export LOGTO_ACCESS_TOKEN=\"$ACCESS_TOKEN\"" > logto_env.sh
    echo "export LOGTO_REFRESH_TOKEN=\"$REFRESH_TOKEN\"" >> logto_env.sh
    echo "export LOGTO_ID_TOKEN=\"$ID_TOKEN\"" >> logto_env.sh
    echo "export LOGTO_USER_ID=\"$USER_ID\"" >> logto_env.sh
    echo "export LOGTO_LOGTO_ID=\"$LOGTO_ID\"" >> logto_env.sh
    log "SUCCESS" "令牌信息已导出到 logto_env.sh"
}

# API测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    local expected_status=${5:-200}
    local skip_on_error=${6:-false}
    
    log "INFO" "测试API: $description"
    log "DEBUG" "请求: $method $API_BASE$endpoint"
    
    if [ -n "$data" ]; then
        log "DEBUG" "请求数据: $data"
    fi
    
    local curl_cmd="curl -s -i -X $method"
    curl_cmd="$curl_cmd -H \"Authorization: Bearer $ACCESS_TOKEN\""
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H \"Content-Type: application/json\" -d '$data'"
    fi
    
    curl_cmd="$curl_cmd \"$API_BASE$endpoint\""
    
    log "DEBUG" "执行命令: $curl_cmd"
    
    # 执行curl命令
    HTTP_RESPONSE=$(eval $curl_cmd)
    
    # 分离状态码和响应体
    HTTP_STATUS=$(echo "$HTTP_RESPONSE" | grep -E "^HTTP" | awk '{print $2}')
    RESPONSE_BODY=$(echo "$HTTP_RESPONSE" | awk 'BEGIN{RS="\r\n\r\n";ORS="\n\n"} NR==2')
    
    log "DEBUG" "响应状态码: $HTTP_STATUS"
    log "DEBUG" "响应体: $RESPONSE_BODY"
    
    # 记录到日志文件
    echo "-----------------------------------" >> $LOG_FILE
    echo "API测试: $description" >> $LOG_FILE
    echo "请求: $method $API_BASE$endpoint" >> $LOG_FILE
    if [ -n "$data" ]; then
        echo "请求数据: $data" >> $LOG_FILE
    fi
    echo "响应状态码: $HTTP_STATUS" >> $LOG_FILE
    echo "响应体: $RESPONSE_BODY" >> $LOG_FILE
    echo "-----------------------------------" >> $LOG_FILE
    
    # 检查状态码是否符合预期
    if [ "$HTTP_STATUS" = "$expected_status" ]; then
        log "SUCCESS" "✓ API测试通过: $description"
        return 0
    else
        if [ "$skip_on_error" = "true" ]; then
            log "WARNING" "! API测试跳过: $description (状态码: $HTTP_STATUS, 预期: $expected_status)"
            return 2
        else
            log "ERROR" "✗ API测试失败: $description (状态码: $HTTP_STATUS, 预期: $expected_status)"
            return 1
        fi
    fi
}

# 显示使用说明
log "INFO" "Logto 授权测试脚本"
log "INFO" "============================="

# 检查是否已有token文件
if [ -f ".logto_tokens.json" ]; then
    log "INFO" "发现现有token文件。您想要："
    echo -e "1) 使用现有token继续测试"
    echo -e "2) 尝试刷新token"
    echo -e "3) 获取全新token"
    read -r CHOICE
    
    if [ "$CHOICE" = "1" ]; then
        JSON_RESPONSE=$(cat .logto_tokens.json)
        log "INFO" "使用现有token继续..."
        
        # 检查token是否有效
        ACCESS_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.access_token')
        if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
            log "ERROR" "无效的token文件，请重新获取token"
            exit 1
        fi
        
        # 提取其他token信息
        REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
        ID_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.id_token')
        USER_ID=$(echo "$JSON_RESPONSE" | jq -r '.user_id')
        LOGTO_ID=$(echo "$JSON_RESPONSE" | jq -r '.logto_id')
        
    elif [ "$CHOICE" = "2" ]; then
        # 尝试刷新token
        JSON_RESPONSE=$(cat .logto_tokens.json)
        REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
        
        if [ -z "$REFRESH_TOKEN" ] || [ "$REFRESH_TOKEN" = "null" ]; then
            log "ERROR" "无效的refresh_token，请重新获取token"
            exit 1
        fi
        
        log "INFO" "尝试刷新token..."
        REFRESH_DATA="{\"refresh_token\":\"$REFRESH_TOKEN\"}"
        REFRESH_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d "$REFRESH_DATA" "$API_BASE/api/refresh")
        
        # 检查刷新是否成功
        NEW_ACCESS_TOKEN=$(echo "$REFRESH_RESPONSE" | jq -r '.access_token')
        if [ -z "$NEW_ACCESS_TOKEN" ] || [ "$NEW_ACCESS_TOKEN" = "null" ]; then
            log "ERROR" "刷新token失败，请重新获取token"
            echo "$REFRESH_RESPONSE"
            exit 1
        fi
        
        log "SUCCESS" "刷新token成功"
        save_tokens "$REFRESH_RESPONSE"
        JSON_RESPONSE=$REFRESH_RESPONSE
        
        # 提取token信息
        ACCESS_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.access_token')
        REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
        ID_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.id_token')
        USER_ID=$(echo "$JSON_RESPONSE" | jq -r '.user_id')
        LOGTO_ID=$(echo "$JSON_RESPONSE" | jq -r '.logto_id')
        
    else
        # 获取新token
        log "INFO" "获取新token..."
        
        # 生成随机state
        STATE=$(openssl rand -hex 16)
        
        # 构建授权URL
        AUTH_URL="$LOGTO_BASE/oidc/auth?client_id=$CLIENT_ID&redirect_uri=$REDIRECT_URI&response_type=code&scope=openid+profile+email+offline_access&state=$STATE"
        
        log "INFO" "请在浏览器中打开以下URL并完成登录："
        echo -e "${CYAN}$AUTH_URL${NC}"
        
        log "INFO" "登录完成后，请将重定向URL中的code参数粘贴到这里："
        read -r CODE
        
        if [ -z "$CODE" ]; then
            log "ERROR" "未提供授权码"
            exit 1
        fi
        
        # 使用授权码获取token
        log "INFO" "使用授权码获取token..."
        TOKEN_RESPONSE=$(curl -s "$API_BASE/api/callback?code=$CODE&state=$STATE")
        
        # 检查token响应
        ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
        if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
            log "ERROR" "获取token失败"
            echo "$TOKEN_RESPONSE"
            exit 1
        fi
        
        log "SUCCESS" "获取token成功"
        save_tokens "$TOKEN_RESPONSE"
        JSON_RESPONSE=$TOKEN_RESPONSE
        
        # 提取token信息
        ACCESS_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.access_token')
        REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
        ID_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.id_token')
        USER_ID=$(echo "$JSON_RESPONSE" | jq -r '.user_id')
        LOGTO_ID=$(echo "$JSON_RESPONSE" | jq -r '.logto_id')
    fi
else
    # 没有现有token，获取新token
    log "INFO" "未发现token文件，将获取新token..."
    
    # 生成随机state
    STATE=$(openssl rand -hex 16)
    
    # 构建授权URL
    AUTH_URL="$LOGTO_BASE/oidc/auth?client_id=$CLIENT_ID&redirect_uri=$REDIRECT_URI&response_type=code&scope=openid+profile+email+offline_access&state=$STATE"
    
    log "INFO" "请在浏览器中打开以下URL并完成登录："
    echo -e "${CYAN}$AUTH_URL${NC}"
    
    log "INFO" "登录完成后，请将重定向URL中的code参数粘贴到这里："
    read -r CODE
    echo "CODE: $CODE"
    
    if [ -z "$CODE" ]; then
        log "ERROR" "未提供授权码"
        exit 1
    fi
    
    # 使用授权码获取token
    log "INFO" "使用授权码获取token..."
    TOKEN_RESPONSE=$(curl -s "$API_BASE/api/callback?code=$CODE&state=$STATE")
    
    # 检查token响应
    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
    if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
        log "ERROR" "获取token失败"
        echo "$TOKEN_RESPONSE"
        exit 1
    fi
    
    log "SUCCESS" "获取token成功"
    save_tokens "$TOKEN_RESPONSE"
    JSON_RESPONSE=$TOKEN_RESPONSE
    
    # 提取token信息
    ACCESS_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.access_token')
    REFRESH_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.refresh_token')
    ID_TOKEN=$(echo "$JSON_RESPONSE" | jq -r '.id_token')
    USER_ID=$(echo "$JSON_RESPONSE" | jq -r '.user_id')
    LOGTO_ID=$(echo "$JSON_RESPONSE" | jq -r '.logto_id')
fi

# 显示token信息
log "INFO" "Token信息："
log "INFO" "Access Token: ${ACCESS_TOKEN:0:20}..."
log "INFO" "User ID: $USER_ID"
log "INFO" "Logto ID: $LOGTO_ID"

# 开始API测试
log "INFO" "开始API测试..."
log "INFO" "============================="

# 测试用户信息API
log "INFO" "4. 测试用户信息API..."
test_api "GET" "/api/user/info" "获取用户信息"

# 测试用户列表API
log "INFO" "5. 测试用户列表API..."
test_api "GET" "/api/users" "获取用户列表"

# 测试获取特定用户API
log "INFO" "6. 测试获取特定用户API..."
test_api "GET" "/api/users/$USER_ID" "获取特定用户信息"

# 测试获取用户配置文件API
log "INFO" "7. 测试获取用户配置文件API..."
test_api "GET" "/api/users/$USER_ID/profile" "获取用户配置文件" "" "200" "true"

# 测试更新用户配置文件API
log "INFO" "8. 测试更新用户配置文件API..."
PROFILE_DATA="{\"nickname\":\"测试用户\",\"avatar\":\"https://example.com/avatar.jpg\",\"bio\":\"这是一个测试用户\"}"
test_api "PUT" "/api/users/$USER_ID/profile" "更新用户配置文件" "$PROFILE_DATA" "200" "true"

# 测试获取用户设置API
log "INFO" "9. 测试获取用户设置API..."
test_api "GET" "/api/users/$USER_ID/settings" "获取用户设置" "" "200" "true"

# 测试更新用户设置API
log "INFO" "10. 测试更新用户设置API..."
SETTINGS_DATA="{\"language\":\"zh-CN\",\"theme\":\"dark\",\"notifications_enabled\":true}"
test_api "PUT" "/api/users/$USER_ID/settings" "更新用户设置" "$SETTINGS_DATA" "200" "true"

# 测试获取用户通知API
log "INFO" "11. 测试获取用户通知API..."
test_api "GET" "/api/users/$USER_ID/notifications" "获取用户通知"

# 测试获取用户设备API
log "INFO" "12. 测试获取用户设备API..."
test_api "GET" "/api/users/$USER_ID/devices" "获取用户设备"

# 测试创建猫API
log "INFO" "13. 测试创建猫API..."
CAT_DATA="{\"user_id\":\"$USER_ID\",\"name\":\"测试猫咪\",\"breed\":\"中华田园猫\",\"birth_date\":\"2020-01-01\",\"gender\":1,\"weight\":4.5}"
test_api "POST" "/api/cats" "创建猫" "$CAT_DATA" "201" "true"

# 获取猫ID
log "INFO" "获取用户的猫列表..."
HTTP_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" "$API_BASE/api/cats")
CATS_LIST=$(echo "$HTTP_RESPONSE" | jq '.')
log "DEBUG" "猫列表: $CATS_LIST"

# 测试家庭组功能
log "INFO" "14. 测试创建家庭组..."
GROUP_DATA="{\"group_name\":\"测试家庭组\",\"description\":\"通过脚本创建的测试家庭组\"}"
GROUP_RESPONSE=$(test_api "POST" "/api/family-groups?user_id=$USER_ID" "创建家庭组" "$GROUP_DATA" "200" "true")
GROUP_ID=$(echo "$GROUP_RESPONSE" | jq -r '.data.group_id')

log "INFO" "15. 测试家庭组邀请功能..."
# 您需要提前准备好另一个用户ID作为被邀请者
read -p "请输入被邀请用户ID: " INVITEE_ID
if [ -n "$INVITEE_ID" ]; then
    INV_DATA="{\"invitee_id\":\"$INVITEE_ID\",\"role\":0}"
    test_api "POST" "/api/family-groups/$GROUP_ID/invitations?user_id=$USER_ID" "发送家庭组邀请" "$INV_DATA" "200" "true"
    
    log "INFO" "列出已发送的邀请..."
    test_api "GET" "/api/family-groups/invitations/sent?user_id=$USER_ID" "获取已发送邀请" "" "200" "true"
    
    log "INFO" "提示: 被邀请用户需要登录并查看/处理邀请"
    log "INFO" "  被邀请用户可通过访问: GET /api/family-groups/invitations/received?user_id=$INVITEE_ID 查看邀请"
fi

echo -e "\n${GREEN}测试脚本执行完毕！${NC}"