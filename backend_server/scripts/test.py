#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import time
import argparse
import requests
from typing import Dict, Any, Optional, List

# 颜色定义
class Colors:
    GREEN = '\033[0;32m'
    RED = '\033[0;31m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

class BackendServerTester:
    """Backend Server API 测试器"""
    
    def __init__(self, base_url: str = "https://api.caby.care", api_key: str = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga", verbose: bool = False):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.verbose = verbose
        self.test_results = {}
        
        # 设置请求头
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'BackendServer-Tester/1.0'
        }
    
    def log(self, message: str, color: str = Colors.NC):
        """输出日志消息"""
        print(f"{color}{message}{Colors.NC}")
    
    def log_success(self, message: str):
        """输出成功消息"""
        print(f"{Colors.GREEN}✅ {message}{Colors.NC}")
    
    def log_error(self, message: str):
        """输出错误消息"""
        print(f"{Colors.RED}❌ {message}{Colors.NC}")
    
    def log_warning(self, message: str):
        """输出警告消息"""
        print(f"{Colors.YELLOW}⚠️  {message}{Colors.NC}")
    
    def log_info(self, message: str):
        """输出信息消息"""
        print(f"{Colors.BLUE}ℹ️  {message}{Colors.NC}")
    
    def make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None, 
                    timeout: int = 30) -> tuple[bool, Dict[str, Any]]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers, timeout=timeout)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data, timeout=timeout)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=self.headers, json=data, timeout=timeout)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=self.headers, timeout=timeout)
            else:
                return False, {"error": f"Unsupported method: {method}"}
            
            if response.status_code in [200, 201]:
                try:
                    return True, response.json()
                except json.JSONDecodeError as e:
                    # JSON解析失败，返回原始响应内容和错误信息
                    return False, {
                        "error": f"JSON解析失败: {str(e)}",
                        "status_code": response.status_code,
                        "raw_response": response.text[:1000] if response.text else "(空响应)",
                        "content_type": response.headers.get('content-type', 'unknown'),
                        "response_length": len(response.text) if response.text else 0
                    }
            else:
                try:
                    error_data = response.json()
                    return False, {"status_code": response.status_code, "error": error_data}
                except json.JSONDecodeError:
                    # 处理特殊的HTTP状态码
                    error_msg = response.text if response.text else f"HTTP {response.status_code}"
                    if response.status_code == 504:
                        error_msg = f"Gateway Timeout (504): {error_msg}"
                    elif response.status_code == 502:
                        error_msg = f"Bad Gateway (502): {error_msg}"
                    elif response.status_code == 503:
                        error_msg = f"Service Unavailable (503): {error_msg}"

                    return False, {
                        "status_code": response.status_code,
                        "error": error_msg,
                        "content_type": response.headers.get('content-type', 'unknown')
                    }
                    
        except requests.exceptions.Timeout:
            return False, {"error": "Request timeout", "timeout_seconds": timeout}
        except requests.exceptions.ConnectionError as e:
            return False, {"error": f"Connection error: {str(e)}"}
        except Exception as e:
            return False, {"error": f"Unexpected error: {str(e)}"}
    
    def test_health(self) -> bool:
        """测试健康检查"""
        self.log_info("测试健康检查...")
        
        success, result = self.make_request("GET", "/api/health", timeout=10)
        
        if success and result.get('status') == 'ok':
            self.log_success("健康检查通过")
            self.test_results['health'] = {'success': True, 'status': result.get('status')}
            return True
        else:
            self.log_error(f"健康检查失败: {result.get('error', 'unknown')}")
            self.test_results['health'] = {'success': False, 'error': result.get('error')}
            return False
    
    def test_analyze_endpoint(self, video_id: str = None, device_id: str = "test_device_001", 
                             user_id: str = "022b605dc3421000") -> bool:
        """测试分析端点"""
        self.log_info("测试分析端点...")
        
        if not video_id:
            video_id = f"test_video_{int(time.time())}"
        
        # 构造测试数据
        test_data = {
            "video_id": video_id,
            "device_id": device_id,
            "user_id": user_id,
            "start_time": "2025-01-01T10:00:00Z",
            "end_time": "2025-01-01T10:05:00Z",
            "video_path": f"https://example.com/videos/{video_id}.m3u8",
            "known_cat_ids": ["f3ce1b02b2c1d755421000", "f3ce1b02b40e9477c21000"]
        }
        
        success, result = self.make_request("POST", "/api/analyze", test_data, timeout=120)
        
        if success:
            self.log_success("分析端点调用成功")
            if self.verbose:
                self.log(f"   结果: {json.dumps(result, indent=2, ensure_ascii=False)}", Colors.CYAN)
            
            # 检查返回的关键字段
            if 'video_id' in result and 'animal_id' in result:
                self.log(f"   视频ID: {result.get('video_id')}", Colors.CYAN)
                self.log(f"   识别的动物ID: {result.get('animal_id')}", Colors.CYAN)
                self.log(f"   置信度: {result.get('cat_confidence', 0):.4f}", Colors.CYAN)
                self.log(f"   行为类型: {result.get('behavior_type')}", Colors.CYAN)
            
            self.test_results['analyze'] = {
                'success': True,
                'video_id': result.get('video_id'),
                'animal_id': result.get('animal_id'),
                'confidence': result.get('cat_confidence'),
                'behavior_type': result.get('behavior_type')
            }
            return True
        else:
            self.log_error(f"分析端点调用失败: {result.get('error', 'unknown')}")
            self.test_results['analyze'] = {'success': False, 'error': result.get('error')}
            return False
    
    def test_thumbnail_endpoint(self, folder: str = "2025-01-01T10:00:00Z",
                               bucket: str = "device_test_device_001") -> bool:
        """测试缩略图端点"""
        self.log_info("测试缩略图端点...")

        # 构造缩略图URL
        import urllib.parse
        encoded_folder = urllib.parse.quote(folder)
        thumbnail_url = f"/api/records/videos/thumbnail/{encoded_folder}?bucket={bucket}"

        success, result = self.make_request("GET", thumbnail_url, timeout=30)

        if success:
            self.log_success("缩略图端点调用成功")
            self.test_results['thumbnail'] = {'success': True}
            return True
        else:
            # 缩略图可能不存在，这是正常的
            if result.get('status_code') == 404:
                self.log_warning("缩略图不存在 (404) - 这是正常的")
                self.test_results['thumbnail'] = {'success': True, 'note': 'thumbnail not found (expected)'}
                return True
            else:
                self.log_error(f"缩略图端点调用失败: {result.get('error', 'unknown')}")
                self.test_results['thumbnail'] = {'success': False, 'error': result.get('error')}
                return False

    def test_video_static_endpoint(self, video_id: str = "93c2531d58f3ce1b6861fd9a",
                                  user_id: str = "022b605dc3421000") -> bool:
        """测试视频静态检测端点"""
        self.log_info("测试视频静态检测端点...")

        # 构造请求数据
        test_data = {
            "user_id": user_id
        }

        # 构造端点URL
        static_url = f"/api/records/videos/static/{video_id}"

        self.log_info(f"使用测试数据:")
        self.log_info(f"  Video ID: {video_id}")
        self.log_info(f"  User ID: {user_id}")
        self.log_info(f"  端点: {static_url}")

        success, result = self.make_request("POST", static_url, test_data, timeout=1800)

        if success:
            is_static = result.get('is_static', False)
            analysis = result.get('analysis', {})

            self.log_success("视频静态检测成功")
            self.log(f"   是否静态: {is_static}", Colors.CYAN)

            if analysis:
                self.log(f"   视频ID: {analysis.get('video_id', 'N/A')}", Colors.CYAN)
                self.log(f"   识别的动物ID: {analysis.get('animal_id', 'N/A')}", Colors.CYAN)
                self.log(f"   置信度: {analysis.get('cat_confidence', 0):.4f}", Colors.CYAN)
                self.log(f"   行为类型: {analysis.get('behavior_type', 'N/A')}", Colors.CYAN)
                self.log(f"   是否异常: {analysis.get('is_abnormal', False)}", Colors.CYAN)

            self.test_results['video_static'] = {
                'success': True,
                'is_static': is_static,
                'analysis': analysis
            }
            return True
        else:
            error_msg = result.get('error', 'unknown')

            # 显示详细的错误信息用于调试
            if self.verbose:
                self.log_error("详细错误信息:")
                for key, value in result.items():
                    self.log_error(f"  {key}: {value}")

            # 检查是否是预期的错误（视频记录不存在等）
            if 'failed to get video record' in str(error_msg) or 'not found' in str(error_msg).lower():
                self.log_warning("视频记录不存在 - 这可能是正常的（使用测试数据）")
                self.log_info("✅ 静态检测API端点正常工作，失败原因是缺少测试视频记录")

                self.test_results['video_static'] = {
                    'success': True,
                    'note': 'Expected failure due to missing video record',
                    'error': error_msg,
                    'api_integration': 'working'
                }
                return True
            else:
                self.log_error(f"视频静态检测失败: {error_msg}")
                self.test_results['video_static'] = {'success': False, 'error': error_msg, 'debug_info': result}
                return False
    
    def test_batch_analyze(self, video_ids: List[str], device_id: str = "test_device_001",
                          user_id: str = "022b605dc3421000") -> bool:
        """批量测试分析功能"""
        self.log_info(f"批量测试分析功能 ({len(video_ids)} 个视频)...")

        success_count = 0
        total_count = len(video_ids)

        for i, video_id in enumerate(video_ids):
            self.log_info(f"测试视频 {i+1}/{total_count}: {video_id}")

            test_data = {
                "video_id": video_id,
                "device_id": device_id,
                "user_id": user_id,
                "start_time": f"2025-01-{(i%30)+1:02d}T10:00:00Z",
                "end_time": f"2025-01-{(i%30)+1:02d}T10:05:00Z",
                "video_path": f"https://example.com/videos/{video_id}.m3u8",
                "known_cat_ids": ["f3ce1b02b2c1d755421000", "f3ce1b02b40e9477c21000"]
            }

            success, result = self.make_request("POST", "/api/analyze", test_data, timeout=120)

            if success:
                success_count += 1
                animal_id = result.get('animal_id', 'unknown')
                confidence = result.get('cat_confidence', 0)
                self.log(f"   ✅ 成功: {animal_id} (置信度: {confidence:.4f})", Colors.GREEN)
            else:
                self.log(f"   ❌ 失败: {result.get('error', 'unknown')}", Colors.RED)

            # 短暂延迟避免过载
            time.sleep(0.5)

        success_rate = success_count / total_count
        self.log_info(f"批量测试完成: {success_count}/{total_count} 成功 ({success_rate:.1%})")

        self.test_results['batch_analyze'] = {
            'success': success_rate > 0.8,  # 80%成功率认为通过
            'success_count': success_count,
            'total_count': total_count,
            'success_rate': success_rate
        }

        return success_rate > 0.8

    def test_batch_video_static(self, video_ids: List[str], user_id: str = "022b605dc3421000") -> bool:
        """批量测试视频静态检测功能"""
        self.log_info(f"批量测试视频静态检测功能 ({len(video_ids)} 个视频)...")

        success_count = 0
        static_count = 0
        total_count = len(video_ids)
        failed_videos = []

        for i, video_id in enumerate(video_ids):
            self.log_info(f"测试视频 {i+1}/{total_count}: {video_id}")

            # 构造请求数据
            test_data = {
                "user_id": user_id
            }

            # 构造端点URL
            static_url = f"/api/records/videos/static/{video_id}"

            success, result = self.make_request("POST", static_url, test_data, timeout=300)  # 增加到5分钟

            if success:
                success_count += 1
                is_static = result.get('is_static', False)
                analysis = result.get('analysis', {})

                if is_static:
                    static_count += 1
                    self.log(f"   ✅ 静态视频: {video_id}", Colors.GREEN)
                else:
                    self.log(f"   ✅ 非静态视频: {video_id}", Colors.CYAN)

                # 显示分析结果
                if analysis:
                    animal_id = analysis.get('animal_id', 'N/A')
                    confidence = analysis.get('cat_confidence', 0)
                    behavior_type = analysis.get('behavior_type', 'N/A')
                    self.log(f"      动物ID: {animal_id}, 置信度: {confidence:.4f}, 行为: {behavior_type}", Colors.CYAN)

            else:
                error_msg = result.get('error', 'unknown')

                # 显示详细的错误信息用于调试
                if self.verbose:
                    self.log(f"   🔍 详细错误信息 ({video_id}):", Colors.YELLOW)
                    for key, value in result.items():
                        if key == 'raw_response' and len(str(value)) > 200:
                            self.log(f"     {key}: {str(value)[:200]}...", Colors.YELLOW)
                        else:
                            self.log(f"     {key}: {value}", Colors.YELLOW)

                # 检查是否是预期的错误（视频记录不存在等）
                if 'failed to get video record' in str(error_msg) or 'not found' in str(error_msg).lower():
                    success_count += 1  # 认为API正常工作
                    self.log(f"   ⚠️  视频记录不存在: {video_id} (API正常)", Colors.YELLOW)
                elif 'timeout' in str(error_msg).lower():
                    # 超时错误，但不计为API失败
                    self.log(f"   ⏰ 超时: {video_id} - {error_msg}", Colors.YELLOW)
                    failed_videos.append({"video_id": video_id, "error": error_msg, "error_type": "timeout"})
                else:
                    failed_videos.append({"video_id": video_id, "error": error_msg, "debug_info": result})
                    self.log(f"   ❌ 失败: {video_id} - {error_msg}", Colors.RED)

            # 短暂延迟避免过载
            time.sleep(0.5)

        success_rate = success_count / total_count
        static_rate = static_count / total_count if total_count > 0 else 0

        self.log_info(f"批量静态检测完成:")
        self.log_info(f"  总数: {total_count}")
        self.log_info(f"  成功: {success_count} ({success_rate:.1%})")
        self.log_info(f"  静态视频: {static_count} ({static_rate:.1%})")
        self.log_info(f"  失败: {len(failed_videos)}")

        if failed_videos:
            self.log_warning("失败的视频:")
            for failed in failed_videos[:5]:  # 只显示前5个失败的
                self.log_warning(f"  {failed['video_id']}: {failed['error']}")
            if len(failed_videos) > 5:
                self.log_warning(f"  ... 还有 {len(failed_videos) - 5} 个失败")

        self.test_results['batch_video_static'] = {
            'success': success_rate > 0.8,  # 80%成功率认为通过
            'success_count': success_count,
            'total_count': total_count,
            'success_rate': success_rate,
            'static_count': static_count,
            'static_rate': static_rate,
            'failed_videos': failed_videos
        }

        return success_rate > 0.8

    def load_video_ids_from_json(self, json_file: str) -> List[str]:
        """从JSON文件加载video_id列表"""
        video_ids = []

        if not os.path.exists(json_file):
            self.log_error(f"JSON文件不存在: {json_file}")
            return video_ids

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 支持多种JSON格式
            if isinstance(data, list):
                # 直接是video_id列表: ["video1", "video2", ...]
                for item in data:
                    if isinstance(item, str):
                        video_ids.append(item)
                    elif isinstance(item, dict) and 'video_id' in item:
                        video_ids.append(item['video_id'])
            elif isinstance(data, dict):
                # 检查是否是import_result格式
                if 'imported_records' in data:
                    for record in data['imported_records']:
                        if 'video_id' in record:
                            video_ids.append(record['video_id'])
                # 检查是否是record_shit格式
                elif 'record_shit' in data:
                    for record in data['record_shit']:
                        if 'video_id' in record:
                            video_ids.append(record['video_id'])
                # 检查是否直接包含video_ids字段
                elif 'video_ids' in data:
                    video_ids = data['video_ids']
                # 检查是否有video_id字段
                elif 'video_id' in data:
                    video_ids.append(data['video_id'])

            self.log_success(f"从JSON文件加载了 {len(video_ids)} 个video_id")
            if self.verbose and video_ids:
                self.log_info("加载的video_id:")
                for i, vid in enumerate(video_ids[:5]):  # 只显示前5个
                    self.log_info(f"  {i+1}. {vid}")
                if len(video_ids) > 5:
                    self.log_info(f"  ... 还有 {len(video_ids) - 5} 个")

        except json.JSONDecodeError as e:
            self.log_error(f"JSON文件格式错误: {e}")
        except Exception as e:
            self.log_error(f"读取JSON文件时出错: {e}")

        return video_ids

    def load_video_ids_from_txt(self, txt_file: str) -> List[str]:
        """从TXT文件加载video_id列表（每行一个）"""
        video_ids = []

        if not os.path.exists(txt_file):
            self.log_error(f"TXT文件不存在: {txt_file}")
            return video_ids

        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):  # 忽略空行和注释行
                        video_ids.append(line)

            self.log_success(f"从TXT文件加载了 {len(video_ids)} 个video_id")
            if self.verbose and video_ids:
                self.log_info("加载的video_id:")
                for i, vid in enumerate(video_ids[:5]):  # 只显示前5个
                    self.log_info(f"  {i+1}. {vid}")
                if len(video_ids) > 5:
                    self.log_info(f"  ... 还有 {len(video_ids) - 5} 个")

        except Exception as e:
            self.log_error(f"读取TXT文件时出错: {e}")

        return video_ids

    def run_all_tests(self, video_id: str = None) -> Dict[str, bool]:
        """运行所有测试"""
        print(f"{Colors.WHITE}🏠 Backend Server 综合测试{Colors.NC}")
        print("=" * 60)

        if video_id:
            print(f"{Colors.CYAN}📹 使用指定视频ID: {video_id}{Colors.NC}")
        else:
            print(f"{Colors.CYAN}📹 使用生成的测试视频ID{Colors.NC}")
        print()

        results = {}

        # 1. 健康检查
        results['health'] = self.test_health()
        print()

        if not results['health']:
            self.log_error("健康检查失败，停止后续测试")
            return results

        # 2. 分析端点测试
        results['analyze'] = self.test_analyze_endpoint(video_id=video_id)
        print()

        # 3. 缩略图端点测试
        results['thumbnail'] = self.test_thumbnail_endpoint()
        print()

        # 4. 视频静态检测端点测试
        results['video_static'] = self.test_video_static_endpoint(video_id=video_id)
        print()

        return results

    def run_batch_test(self, count: int = 10) -> Dict[str, bool]:
        """运行批量测试"""
        print(f"{Colors.WHITE}🔄 Backend Server 批量测试 ({count} 个记录){Colors.NC}")
        print("=" * 60)

        results = {}

        # 1. 健康检查
        results['health'] = self.test_health()
        print()

        if not results['health']:
            self.log_error("健康检查失败，停止后续测试")
            return results

        # 2. 生成测试视频ID列表
        video_ids = [f"batch_test_video_{i:04d}_{int(time.time())}" for i in range(count)]

        # 3. 批量分析测试
        results['batch_analyze'] = self.test_batch_analyze(video_ids)
        print()

        return results

    def run_batch_video_static_test(self, video_ids_file: str, user_id: str = "022b605dc3421000") -> Dict[str, bool]:
        """运行批量视频静态检测测试"""
        print(f"{Colors.WHITE}🔍 Backend Server 批量视频静态检测测试{Colors.NC}")
        print("=" * 60)

        results = {}

        # 1. 健康检查
        results['health'] = self.test_health()
        print()

        if not results['health']:
            self.log_error("健康检查失败，停止后续测试")
            return results

        # 2. 加载video_id列表
        if video_ids_file.endswith('.json'):
            video_ids = self.load_video_ids_from_json(video_ids_file)
        elif video_ids_file.endswith('.txt'):
            video_ids = self.load_video_ids_from_txt(video_ids_file)
        else:
            # 尝试自动检测格式
            try:
                with open(video_ids_file, 'r') as f:
                    first_line = f.readline().strip()
                    if first_line.startswith('{') or first_line.startswith('['):
                        video_ids = self.load_video_ids_from_json(video_ids_file)
                    else:
                        video_ids = self.load_video_ids_from_txt(video_ids_file)
            except Exception as e:
                self.log_error(f"无法读取文件 {video_ids_file}: {e}")
                return results

        if not video_ids:
            self.log_error("未找到任何video_id，请检查文件格式")
            return results

        print(f"{Colors.CYAN}📹 将测试 {len(video_ids)} 个视频的静态检测{Colors.NC}")
        print()

        # 3. 批量视频静态检测测试
        results['batch_video_static'] = self.test_batch_video_static(video_ids, user_id)
        print()

        return results

    def print_summary(self, results: Dict[str, bool]):
        """打印测试总结"""
        print("=" * 60)
        print(f"{Colors.WHITE}📋 测试结果总结{Colors.NC}")
        print("=" * 60)

        passed = sum(1 for result in results.values() if result)
        total = len(results)

        test_names = {
            'health': '健康检查',
            'analyze': '分析端点',
            'thumbnail': '缩略图端点',
            'video_static': '视频静态检测',
            'batch_analyze': '批量分析',
            'batch_video_static': '批量视频静态检测'
        }

        for test_key, result in results.items():
            test_name = test_names.get(test_key, test_key)
            status = f"{Colors.GREEN}✅ 通过{Colors.NC}" if result else f"{Colors.RED}❌ 失败{Colors.NC}"
            print(f"  {test_name}: {status}")

        print()
        if passed == total:
            print(f"{Colors.GREEN}🎉 所有测试通过! Backend Server API运行正常!{Colors.NC}")
        else:
            print(f"{Colors.YELLOW}⚠️  {passed}/{total} 测试通过{Colors.NC}")

        # 保存测试结果
        try:
            with open('test_results.json', 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            print(f"{Colors.BLUE}📄 详细结果已保存到 test_results.json{Colors.NC}")
        except Exception as e:
            self.log_warning(f"保存测试结果失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Backend Server API 测试工具')
    parser.add_argument('tests', nargs='*', default=['all'],
                       choices=['all', 'health', 'analyze', 'thumbnail', 'video_static', 'batch', 'batch_video_static'],
                       help='要运行的测试 (默认: all)')
    parser.add_argument('--url', default='https://api.caby.care',
                       help='API URL (默认: https://api.caby.care)')
    parser.add_argument('--api-key', default='03U66tGbSQtHGrh9IyBDjRYaSeQukFga',
                       help='API密钥 (默认: 03U66tGbSQtHGrh9IyBDjRYaSeQukFga)')
    parser.add_argument('--video-id', help='指定视频ID进行测试')
    parser.add_argument('--batch-count', type=int, default=10,
                       help='批量测试的记录数量 (默认: 10)')
    parser.add_argument('--video-ids-file', help='包含video_id列表的JSON或TXT文件路径')
    parser.add_argument('--user-id', default='022b605dc3421000',
                       help='用户ID (默认: 022b605dc3421000)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')

    args = parser.parse_args()

    # 创建测试器
    tester = BackendServerTester(args.url, args.api_key, args.verbose)

    print(f"{Colors.CYAN}🌐 API URL: {args.url}{Colors.NC}")
    print(f"{Colors.CYAN}🔑 API Key: {args.api_key[:10]}...{args.api_key[-10:]}{Colors.NC}")
    if args.video_id:
        print(f"{Colors.CYAN}📹 视频ID: {args.video_id}{Colors.NC}")
    print()

    # 确定要运行的测试
    if 'all' in args.tests:
        results = tester.run_all_tests(video_id=args.video_id)
    elif 'batch' in args.tests:
        results = tester.run_batch_test(count=args.batch_count)
    elif 'batch_video_static' in args.tests:
        if not args.video_ids_file:
            print(f"{Colors.RED}❌ 批量视频静态检测需要指定 --video-ids-file 参数{Colors.NC}")
            return
        results = tester.run_batch_video_static_test(args.video_ids_file, args.user_id)
    else:
        results = {}

        for test in args.tests:
            if test == 'health':
                results['health'] = tester.test_health()
            elif test == 'analyze':
                results['analyze'] = tester.test_analyze_endpoint(video_id=args.video_id)
            elif test == 'thumbnail':
                results['thumbnail'] = tester.test_thumbnail_endpoint()
            elif test == 'video_static':
                results['video_static'] = tester.test_video_static_endpoint(video_id=args.video_id)

            print()

    # 打印总结
    tester.print_summary(results)

if __name__ == "__main__":
    main()
