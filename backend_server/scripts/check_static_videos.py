#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import argparse
import requests
import threading
import concurrent.futures
import logging
import time
from tqdm import tqdm
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("static_video_check.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 默认设置
DEFAULT_API_URL = "http://144.126.146.223:5678/api"
DEFAULT_API_PATH = "/records/videos/static"
DEFAULT_SERVICE_TOKEN = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
DEFAULT_USER_ID = "022b605dc3421000"

class StaticVideoChecker:
    def __init__(self, api_url: str, api_path: str, service_token: str, user_id: str, max_workers: int = 4):
        """
        初始化静态视频检查器

        Args:
            api_url: API基础URL
            api_path: API路径
            service_token: 服务认证令牌
            user_id: 用户ID
            max_workers: 最大线程数
        """
        self.api_url = api_url
        self.api_path = api_path
        self.service_token = service_token
        self.user_id = user_id
        self.max_workers = max_workers
        self.results_lock = threading.Lock()
        self.static_count = 0
        self.failed_count = 0
        self.total_processed = 0
        self.errors = []

    def load_records(self, json_file: str) -> List[Dict[str, Any]]:
        """
        从JSON文件加载记录

        Args:
            json_file: JSON文件路径

        Returns:
            包含视频记录的列表
        """
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                if 'record_shit' in data:
                    return data['record_shit']
                else:
                    logger.error("无法找到 'record_shit' 键在JSON文件中")
                    return []
        except Exception as e:
            logger.error(f"加载JSON文件时出错: {e}")
            return []

    def load_video_ids_txt(self, txt_file: str) -> List[Dict[str, Any]]:
        """
        从txt文件加载video_id列表，每行一个video_id
        """
        records = []
        try:
            with open(txt_file, 'r') as f:
                for line in f:
                    video_id = line.strip()
                    if video_id:
                        records.append({"video_id": video_id})
            return records
        except Exception as e:
            logger.error(f"加载TXT文件时出错: {e}")
            return []

    def check_video_static(self, video_id: str) -> Dict[str, Any]:
        """
        检查视频是否为静态的

        Args:
            video_id: 视频ID

        Returns:
            API响应结果
        """
        url = f"{self.api_url}{self.api_path}/{video_id}"
        headers = {
            "Authorization": f"Bearer {self.service_token}",
            "Content-Type": "application/json"
        }
        payload = {"user_id": self.user_id}

        try:
            response = requests.get(url, headers=headers, json=payload)
            response.raise_for_status()
            return {
                "video_id": video_id,
                "status_code": response.status_code,
                "response": response.json(),
                "success": True
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败 (video_id: {video_id}): {e}")
            return {
                "video_id": video_id,
                "status_code": getattr(e.response, 'status_code', None),
                "response": getattr(e.response, 'text', str(e)),
                "success": False
            }

    def process_record(self, record: Dict[str, Any], pbar: tqdm) -> Dict[str, Any]:
        """
        处理单个记录

        Args:
            record: 视频记录
            pbar: 进度条

        Returns:
            处理结果
        """
        video_id = record.get("video_id")
        if not video_id:
            logger.warning(f"记录缺少视频ID: {record}")
            return {"success": False, "error": "缺少视频ID"}

        result = self.check_video_static(video_id)

        with self.results_lock:
            self.total_processed += 1
            if result["success"]:
                # 检查API响应中是否包含静态视频标志
                is_static = False
                if isinstance(result["response"], dict):
                    api_result = result["response"]
                    if "is_static" in api_result and api_result["is_static"]:
                        is_static = True
                    elif "behavior_type" in api_result and api_result["behavior_type"] == "static_video":
                        is_static = True

                if is_static:
                    self.static_count += 1
                    logger.info(f"静态视频: {video_id}")
            else:
                self.failed_count += 1
                self.errors.append({"video_id": video_id, "error": result.get("response", "未知错误")})
                logger.error(f"处理失败: {video_id} - {result.get('response', '未知错误')}")

        pbar.update(1)
        return result

    def run(self, input_file: str):
        """
        运行静态视频检查

        Args:
            input_file: JSON或TXT文件路径
        """
        if input_file.endswith('.json'):
            records = self.load_records(input_file)
        else:
            records = self.load_video_ids_txt(input_file)
        if not records:
            logger.error("未找到记录,请检查输入文件格式")
            return

        # 健康检查
        try:
            health_check = requests.get(f"{self.api_url}/health")
            health_check.raise_for_status()
            logger.info(f"服务健康检查成功: {health_check.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"服务健康检查失败: {e}")
            if input("服务似乎不可用,是否继续? (y/N): ").lower() != 'y':
                return

        # 开始处理
        start_time = time.time()
        total_records = len(records)
        logger.info(f"开始处理 {total_records} 条记录,使用 {self.max_workers} 个线程")

        # 创建进度条
        with tqdm(total=total_records, desc="处理视频") as pbar:
            # 使用线程池并发处理
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(self.process_record, record, pbar) for record in records]
                concurrent.futures.wait(futures)

        # 统计结果
        end_time = time.time()
        processing_time = end_time - start_time

        logger.info(f"处理完成! 花费时间: {processing_time:.2f} 秒")
        logger.info(f"总记录数: {total_records}")
        logger.info(f"处理成功: {self.total_processed - self.failed_count}")
        logger.info(f"处理失败: {self.failed_count}")
        logger.info(f"静态视频数: {self.static_count}")

        # 保存错误记录
        if self.errors:
            error_file = "static_video_errors.json"
            with open(error_file, 'w') as f:
                json.dump({"errors": self.errors}, f, indent=2)
            logger.info(f"错误记录已保存到 {error_file}")

def main():
    parser = argparse.ArgumentParser(description="检查视频是否为静态视频 (支持JSON或TXT输入)")
    parser.add_argument("input_file", help="记录ID的JSON文件路径，或每行一个video_id的TXT文件")
    parser.add_argument("--api-url", default=DEFAULT_API_URL,
                        help=f"API基础URL (默认: {DEFAULT_API_URL})")
    parser.add_argument("--api-path", default=DEFAULT_API_PATH,
                        help=f"API路径 (默认: {DEFAULT_API_PATH})")
    parser.add_argument("--token", default=DEFAULT_SERVICE_TOKEN,
                        help="服务认证令牌")
    parser.add_argument("--user-id", default=DEFAULT_USER_ID,
                        help=f"用户ID (默认: {DEFAULT_USER_ID})")
    parser.add_argument("--workers", type=int, default=4,
                        help="并发线程数 (默认: 4)")

    args = parser.parse_args()

    checker = StaticVideoChecker(
        api_url=args.api_url,
        api_path=args.api_path,
        service_token=args.token,
        user_id=args.user_id,
        max_workers=args.workers
    )

    checker.run(args.input_file)

if __name__ == "__main__":
    main()