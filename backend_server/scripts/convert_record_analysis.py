#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
转换 record_analysis_*.json 文件为 video_ids 格式
支持多种输出格式：简单数组、video_ids对象格式
"""

import json
import argparse
import os
import sys
from typing import List, Dict, Any


def load_record_analysis(input_file: str) -> List[Dict[str, Any]]:
    """
    加载 record_analysis JSON 文件
    
    Args:
        input_file: 输入文件路径
        
    Returns:
        记录列表
    """
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件不存在 {input_file}")
        return []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'record_analysis' in data:
            records = data['record_analysis']
            print(f"✅ 成功加载 {len(records)} 条记录")
            return records
        else:
            print(f"❌ 错误: 文件中未找到 'record_analysis' 字段")
            return []
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return []
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return []


def extract_video_ids(records: List[Dict[str, Any]], filter_unknown: bool = False) -> List[str]:
    """
    从记录中提取 video_id 列表
    
    Args:
        records: 记录列表
        filter_unknown: 是否过滤掉 animal_id 为 "unknown" 的记录
        
    Returns:
        video_id 列表
    """
    video_ids = []
    filtered_count = 0
    
    for record in records:
        video_id = record.get('video_id')
        if not video_id:
            continue
            
        if filter_unknown:
            animal_id = record.get('animal_id', '')
            if animal_id == 'unknown':
                filtered_count += 1
                continue
        
        video_ids.append(video_id)
    
    if filter_unknown and filtered_count > 0:
        print(f"ℹ️  过滤掉 {filtered_count} 个 animal_id 为 'unknown' 的记录")
    
    return video_ids


def save_video_ids(video_ids: List[str], output_file: str, format_type: str = "object"):
    """
    保存 video_id 列表到文件
    
    Args:
        video_ids: video_id 列表
        output_file: 输出文件路径
        format_type: 输出格式 ("object" 或 "array")
    """
    try:
        if format_type == "object":
            # {"video_ids": [...]} 格式
            output_data = {"video_ids": video_ids}
        else:
            # 直接数组 [...] 格式
            output_data = video_ids
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 成功保存 {len(video_ids)} 个 video_id 到 {output_file}")
        
    except Exception as e:
        print(f"❌ 保存文件时出错: {e}")


def save_video_ids_txt(video_ids: List[str], output_file: str):
    """
    保存 video_id 列表到 TXT 文件
    
    Args:
        video_ids: video_id 列表
        output_file: 输出文件路径
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Video ID 列表\n")
            f.write(f"# 总数: {len(video_ids)}\n")
            f.write("# 每行一个 video_id\n\n")
            
            for video_id in video_ids:
                f.write(f"{video_id}\n")
        
        print(f"✅ 成功保存 {len(video_ids)} 个 video_id 到 {output_file}")
        
    except Exception as e:
        print(f"❌ 保存文件时出错: {e}")


def print_statistics(records: List[Dict[str, Any]], video_ids: List[str]):
    """
    打印统计信息
    
    Args:
        records: 原始记录列表
        video_ids: 提取的 video_id 列表
    """
    print("\n📊 统计信息:")
    print(f"  总记录数: {len(records)}")
    print(f"  有效 video_id: {len(video_ids)}")
    
    if records:
        # 统计 animal_id
        animal_ids = {}
        behavior_types = {}
        
        for record in records:
            animal_id = record.get('animal_id', 'unknown')
            behavior_type = record.get('behavior_type', 'unknown')
            
            animal_ids[animal_id] = animal_ids.get(animal_id, 0) + 1
            behavior_types[behavior_type] = behavior_types.get(behavior_type, 0) + 1
        
        print(f"  Animal ID 分布:")
        for aid, count in sorted(animal_ids.items()):
            print(f"    {aid}: {count}")
        
        print(f"  行为类型分布:")
        for bt, count in sorted(behavior_types.items()):
            print(f"    {bt}: {count}")


def main():
    parser = argparse.ArgumentParser(description='转换 record_analysis_*.json 文件为 video_ids 格式')
    parser.add_argument('input_file', help='输入的 record_analysis JSON 文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径 (默认: 基于输入文件名生成)')
    parser.add_argument('-f', '--format', choices=['object', 'array', 'txt'], default='object',
                       help='输出格式: object={"video_ids":[...]}, array=[...], txt=文本文件 (默认: object)')
    parser.add_argument('--filter-unknown', action='store_true',
                       help='过滤掉 animal_id 为 "unknown" 的记录')
    parser.add_argument('--stats', action='store_true',
                       help='显示详细统计信息')
    
    args = parser.parse_args()
    
    # 加载输入文件
    records = load_record_analysis(args.input_file)
    if not records:
        sys.exit(1)
    
    # 提取 video_id
    video_ids = extract_video_ids(records, args.filter_unknown)
    if not video_ids:
        print("❌ 未找到有效的 video_id")
        sys.exit(1)
    
    # 生成输出文件名
    if args.output:
        output_file = args.output
    else:
        base_name = os.path.splitext(os.path.basename(args.input_file))[0]
        if args.format == 'txt':
            output_file = f"{base_name}_video_ids.txt"
        else:
            output_file = f"{base_name}_video_ids.json"
    
    # 保存结果
    if args.format == 'txt':
        save_video_ids_txt(video_ids, output_file)
    else:
        save_video_ids(video_ids, output_file, args.format)
    
    # 显示统计信息
    if args.stats:
        print_statistics(records, video_ids)
    
    print(f"\n🎉 转换完成!")
    print(f"📁 输出文件: {output_file}")
    print(f"📊 video_id 数量: {len(video_ids)}")
    
    # 显示使用示例
    print(f"\n💡 使用示例:")
    print(f"python backend_server/scripts/test.py batch_video_static --video-ids-file {output_file}")


if __name__ == "__main__":
    main()
