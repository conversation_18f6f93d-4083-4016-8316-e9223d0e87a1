#!/bin/bash

# 设置测试环境的脚本
# 用于为简化测试脚本提供认证token

echo "=== 设置测试环境 ==="

# 检查是否存在logto环境文件
if [ -f "logto_env.sh" ]; then
    echo "发现 logto_env.sh 文件，加载token..."
    source logto_env.sh
    
    if [ -n "$LOGTO_ACCESS_TOKEN" ]; then
        echo "✓ 成功加载Access Token: ${LOGTO_ACCESS_TOKEN:0:20}..."
        echo "✓ 用户ID: $LOGTO_USER_ID"
        
        # 导出环境变量供其他脚本使用
        export ACCESS_TOKEN="$LOGTO_ACCESS_TOKEN"
        export USER_ID="$LOGTO_USER_ID"
        
        echo ""
        echo "环境变量已设置，现在可以运行以下测试："
        echo "1. 带认证的完整测试: ./test_create_cat_with_auth.sh"
        echo "2. 使用现有token的简化测试:"
        echo "   export ACCESS_TOKEN=\"$LOGTO_ACCESS_TOKEN\""
        echo "   # 然后修改 test_create_cat_simple.sh 使用 \$ACCESS_TOKEN"
        echo ""
        echo "或者直接使用curl测试："
        echo "curl -X POST \"http://localhost:5678/api/cats\" \\"
        echo "  -H \"Content-Type: application/json\" \\"
        echo "  -H \"Authorization: Bearer $LOGTO_ACCESS_TOKEN\" \\"
        echo "  -d '{\"name\": \"Test Cat\", \"gender\": 1}'"
        
    else
        echo "✗ logto_env.sh 文件中没有找到有效的token"
        echo "请先运行: ./test_logto.sh 或 ./test_create_cat_with_auth.sh 获取token"
    fi
elif [ -f ".logto_tokens.json" ]; then
    echo "发现 .logto_tokens.json 文件，提取token..."
    
    if command -v jq &> /dev/null; then
        ACCESS_TOKEN=$(cat .logto_tokens.json | jq -r '.access_token')
        USER_ID=$(cat .logto_tokens.json | jq -r '.user_id')
        
        if [ "$ACCESS_TOKEN" != "null" ] && [ -n "$ACCESS_TOKEN" ]; then
            echo "✓ 成功提取Access Token: ${ACCESS_TOKEN:0:20}..."
            echo "✓ 用户ID: $USER_ID"
            
            # 创建logto_env.sh文件
            echo "export LOGTO_ACCESS_TOKEN=\"$ACCESS_TOKEN\"" > logto_env.sh
            echo "export LOGTO_USER_ID=\"$USER_ID\"" >> logto_env.sh
            
            echo "✓ 已创建 logto_env.sh 文件"
            
            # 导出环境变量
            export ACCESS_TOKEN="$ACCESS_TOKEN"
            export USER_ID="$USER_ID"
            
            echo ""
            echo "环境变量已设置！"
        else
            echo "✗ 无法从JSON文件中提取有效token"
        fi
    else
        echo "✗ 需要安装jq来解析JSON文件"
        echo "请运行: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
    fi
else
    echo "✗ 未找到token文件"
    echo "请先运行以下命令之一获取token："
    echo "1. ./test_logto.sh"
    echo "2. ./test_create_cat_with_auth.sh"
fi

echo ""
echo "=== 环境设置完成 ===" 