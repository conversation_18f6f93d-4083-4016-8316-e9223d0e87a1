#!/bin/bash

# Default configuration
DEFAULT_API_URL="http://localhost:8080"
DEFAULT_USER_ID="123"
DEFAULT_TIMEZONE="Asia/Shanghai"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Parse command line arguments
API_URL=$DEFAULT_API_URL
USER_ID=$DEFAULT_USER_ID
TIMEZONE=$DEFAULT_TIMEZONE

usage() {
  echo -e "${YELLOW}Usage: $0 [-u API_URL] [-i USER_ID] [-t TIMEZONE]${NC}"
  echo -e "  -u API_URL   : API base URL (default: $DEFAULT_API_URL)"
  echo -e "  -i USER_ID   : User ID to test with (default: $DEFAULT_USER_ID)"
  echo -e "  -t TIMEZONE  : Timezone to use (default: $DEFAULT_TIMEZONE)"
  echo -e "  -h           : Show this help"
  exit 1
}

while getopts "u:i:t:h" opt; do
  case $opt in
    u) API_URL=$OPTARG ;;
    i) USER_ID=$OPTARG ;;
    t) TIMEZONE=$OPTARG ;;
    h) usage ;;
    *) usage ;;
  esac
done

echo -e "${BLUE}=== Testing Notification API ===${NC}"
echo -e "API URL: $API_URL"
echo -e "User ID: $USER_ID"
echo -e "Timezone: $TIMEZONE"

# Common function to get notification settings
get_notification_settings() {
  local user_id=$1
  echo -e "\n${BLUE}Getting settings for user_id: $user_id${NC}"
  echo "Request: GET $API_URL/notifications/settings?user_id=$user_id"

  local response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$API_URL/notifications/settings?user_id=$user_id")
  local status=$(echo "$response" | grep HTTP_STATUS | cut -d':' -f2)
  local body=$(echo "$response" | sed '/HTTP_STATUS/d')

  if [ "$status" -eq 200 ]; then
    echo -e "${GREEN}SUCCESS (HTTP $status)${NC}"
    echo "Response: $body"
    # Check if the response contains expected fields
    if echo "$body" | grep -q "settings"; then
      echo -e "${GREEN}✓ Response contains settings object${NC}"
    else
      echo -e "${RED}✗ Response doesn't contain settings object${NC}"
    fi
  else
    echo -e "${RED}FAILED (HTTP $status)${NC}"
    echo "Response: $body"
  fi

  # Return the response body for further processing
  echo "$body"
}

# Function to convert minutes to time string
minutes_to_time() {
  local minutes=$1
  local hours=$((minutes / 60))
  local mins=$((minutes % 60))
  printf "%02d:%02d" $hours $mins
}

# Function to convert time string to minutes
time_to_minutes() {
  local time_str=$1
  local hours=${time_str%%:*}
  local minutes=${time_str##*:}
  echo $((hours * 60 + minutes))
}

# Common function to verify notification settings match expected values
verify_settings() {
  local body="$1"
  local expected_enable_daily="$2"
  local expected_enable_stats="$3"
  local expected_quiet_hours_start="$4"
  local expected_quiet_hours_end="$5"
  local expected_timezone="$6"

  echo -e "\n${BLUE}Checking if settings match expected values:${NC}"
  local success=true

  # Check each setting individually to provide better feedback
  if echo "$body" | grep -q "\"enable_daily\":$expected_enable_daily"; then
    echo -e "${GREEN}✓ enable_daily matches expected value: $expected_enable_daily${NC}"
  else
    echo -e "${RED}✗ enable_daily does not match expected value: $expected_enable_daily${NC}"
    success=false
  fi

  if echo "$body" | grep -q "\"enable_stats\":$expected_enable_stats"; then
    echo -e "${GREEN}✓ enable_stats matches expected value: $expected_enable_stats${NC}"
  else
    echo -e "${RED}✗ enable_stats does not match expected value: $expected_enable_stats${NC}"
    success=false
  fi

  if echo "$body" | grep -q "\"quiet_hours_start\":$expected_quiet_hours_start"; then
    echo -e "${GREEN}✓ quiet_hours_start matches expected value: $expected_quiet_hours_start (${BLUE}$(minutes_to_time $expected_quiet_hours_start)${NC})${NC}"
  else
    echo -e "${RED}✗ quiet_hours_start does not match expected value: $expected_quiet_hours_start (${BLUE}$(minutes_to_time $expected_quiet_hours_start)${NC})${NC}"
    success=false
  fi

  if echo "$body" | grep -q "\"quiet_hours_end\":$expected_quiet_hours_end"; then
    echo -e "${GREEN}✓ quiet_hours_end matches expected value: $expected_quiet_hours_end (${BLUE}$(minutes_to_time $expected_quiet_hours_end)${NC})${NC}"
  else
    echo -e "${RED}✗ quiet_hours_end does not match expected value: $expected_quiet_hours_end (${BLUE}$(minutes_to_time $expected_quiet_hours_end)${NC})${NC}"
    success=false
  fi
  
  if echo "$body" | grep -q "\"timezone\":\"$expected_timezone\""; then
    echo -e "${GREEN}✓ timezone matches expected value: $expected_timezone${NC}"
  else
    echo -e "${RED}✗ timezone does not match expected value: $expected_timezone${NC}"
    success=false
  fi

  if [ "$success" = true ]; then
    echo -e "${GREEN}✓ All settings match the expected values${NC}"
  else
    echo -e "${RED}✗ Some settings do not match expected values${NC}"
  fi
}

# Test 1: Get initial notification settings
initial_settings=$(get_notification_settings "$USER_ID")

# Test 2: Update notification settings
echo -e "\n${BLUE}Testing UPDATE settings${NC}"

# Define the values we're updating to for later verification
update_enable_daily=true
update_enable_stats=true
# 设置时间为 20:30 - 08:15
update_quiet_hours_start=1230  # 20:30
update_quiet_hours_end=495     # 08:15

UPDATE_PAYLOAD='{
  "user_id": "'$USER_ID'",
  "enable_daily": '$update_enable_daily',
  "enable_stats": '$update_enable_stats',
  "quiet_hours_start": '$update_quiet_hours_start',
  "quiet_hours_end": '$update_quiet_hours_end',
  "timezone": "'$TIMEZONE'"
}'

echo "Request: PUT $API_URL/notifications/settings"
echo "Payload: $UPDATE_PAYLOAD"
echo "Setting quiet hours to: $(minutes_to_time $update_quiet_hours_start) - $(minutes_to_time $update_quiet_hours_end)"

response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X PUT \
  -H "Content-Type: application/json" \
  -d "$UPDATE_PAYLOAD" \
  "$API_URL/notifications/settings")

status=$(echo "$response" | grep HTTP_STATUS | cut -d':' -f2)
body=$(echo "$response" | sed '/HTTP_STATUS/d')

if [ "$status" -eq 200 ]; then
  echo -e "${GREEN}SUCCESS (HTTP $status)${NC}"
  echo "Response: $body"
  if echo "$body" | grep -q "message"; then
    echo -e "${GREEN}✓ Response contains success message${NC}"
  else
    echo -e "${RED}✗ Response doesn't contain success message${NC}"
  fi
else
  echo -e "${RED}FAILED (HTTP $status)${NC}"
  echo "Response: $body"
fi

# Test 3: Verify settings were updated using our common function
updated_settings=$(get_notification_settings "$USER_ID")
verify_settings "$updated_settings" "$update_enable_daily" "$update_enable_stats" "$update_quiet_hours_start" "$update_quiet_hours_end" "$TIMEZONE"

# Test 4: Test invalid time values - update to check actual behavior
echo -e "\n${BLUE}Testing invalid time values${NC}"

# Test high start time (1440 minutes = 24 hours)
INVALID_START_PAYLOAD='{
  "user_id": "'$USER_ID'",
  "enable_daily": true,
  "enable_stats": true,
  "quiet_hours_start": 1440,
  "quiet_hours_end": 495,
  "timezone": "'$TIMEZONE'"
}'

echo "Request: PUT $API_URL/notifications/settings (High start time: 1440)"
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X PUT \
  -H "Content-Type: application/json" \
  -d "$INVALID_START_PAYLOAD" \
  "$API_URL/notifications/settings")

status=$(echo "$response" | grep HTTP_STATUS | cut -d':' -f2)
body=$(echo "$response" | sed '/HTTP_STATUS/d')

echo -e "Response: $body"

# Check if API accepts the invalid value or silently corrects it
echo -e "${YELLOW}Checking if API accepted or corrected high start time value...${NC}"
settings_after_invalid=$(get_notification_settings "$USER_ID")
if echo "$settings_after_invalid" | grep -q "\"quiet_hours_start\":1440"; then
  echo -e "${YELLOW}NOTE: API accepted the invalid start time (1440 minutes = 24:00)${NC}"
else
  actual_start=$(echo "$settings_after_invalid" | grep -o '"quiet_hours_start":[0-9]*' | cut -d':' -f2)
  echo -e "${YELLOW}API corrected or rejected the invalid start time. Actual value: $actual_start ($(minutes_to_time $actual_start))${NC}"
fi

# Test negative end time (-1 minutes)
INVALID_END_PAYLOAD='{
  "user_id": "'$USER_ID'",
  "enable_daily": true,
  "enable_stats": true,
  "quiet_hours_start": 1230,
  "quiet_hours_end": -1,
  "timezone": "'$TIMEZONE'"
}'

echo -e "\nRequest: PUT $API_URL/notifications/settings (Negative end time: -1)"
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X PUT \
  -H "Content-Type: application/json" \
  -d "$INVALID_END_PAYLOAD" \
  "$API_URL/notifications/settings")

status=$(echo "$response" | grep HTTP_STATUS | cut -d':' -f2)
body=$(echo "$response" | sed '/HTTP_STATUS/d')

echo -e "Response: $body"

# Check if API accepts the negative value or silently corrects it
echo -e "${YELLOW}Checking if API accepted or corrected negative end time value...${NC}"
settings_after_invalid=$(get_notification_settings "$USER_ID")
if echo "$settings_after_invalid" | grep -q "\"quiet_hours_end\":-1"; then
  echo -e "${YELLOW}NOTE: API accepted the invalid end time (-1 minutes)${NC}"
else
  actual_end=$(echo "$settings_after_invalid" | grep -o '"quiet_hours_end":[0-9]*' | cut -d':' -f2)
  echo -e "${YELLOW}API corrected or rejected the invalid end time. Actual value: $actual_end ($(minutes_to_time $actual_end))${NC}"
fi

# Test 5: Reset to valid values
echo -e "\n${BLUE}Resetting to valid values${NC}"
RESET_PAYLOAD='{
  "user_id": "'$USER_ID'",
  "enable_daily": true,
  "enable_stats": true,
  "quiet_hours_start": 1230,
  "quiet_hours_end": 495,
  "timezone": "'$TIMEZONE'"
}'

curl -s -X PUT \
  -H "Content-Type: application/json" \
  -d "$RESET_PAYLOAD" \
  "$API_URL/notifications/settings" > /dev/null

final_settings=$(get_notification_settings "$USER_ID")
verify_settings "$final_settings" "true" "true" "1230" "495" "$TIMEZONE"

echo -e "\n${BLUE}=== Tests Completed ===${NC}"

