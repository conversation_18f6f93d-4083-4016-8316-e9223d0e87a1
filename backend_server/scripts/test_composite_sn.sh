#!/bin/bash

# 复合SN号测试脚本
# 测试格式：8位随机16进制_PCB_SN_时间戳
# 使用方法: ./test_composite_sn.sh [server_url]

SERVER_URL=${1:-"http://localhost:5678"}
API_BASE="$SERVER_URL/api/factory"

# 工厂永久Token
FACTORY_TOKEN="FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

echo "测试复合SN号功能 - 服务器: $SERVER_URL"
echo "SN号格式：8位随机16进制_PCB_SN_时间戳"
echo "=================================================="

# 1. 测试健康检查
echo "1. 测试健康检查..."
curl -s -X GET "$API_BASE/health" | jq '.'
echo ""

# 2. 测试申请复合SN号（单个）
echo "2. 测试申请复合SN号（单个）..."
PCB_SN_1="PCB001234567"
APPLY_RESPONSE_1=$(curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN_1\",
    \"device_type\": \"toilet_v1\",
    \"production_line\": \"line_001\", 
    \"batch_number\": \"batch_composite_001\",
    \"operator\": \"复合SN测试员\",
    \"quantity\": 1,
    \"remark\": \"复合SN号测试 - 单个\"
  }")

echo "$APPLY_RESPONSE_1" | jq '.'

# 提取第一个SN号用于后续测试
FIRST_SN=$(echo "$APPLY_RESPONSE_1" | jq -r '.data.sn_list[0]')
echo "生成的复合SN号: $FIRST_SN"
echo ""

# 3. 验证SN号格式
echo "3. 验证复合SN号格式..."
echo "SN号: $FIRST_SN"

# 分解SN号
IFS='_' read -ra SN_PARTS <<< "$FIRST_SN"
if [ ${#SN_PARTS[@]} -eq 3 ]; then
    RANDOM_HEX="${SN_PARTS[0]}"
    PCB_PART="${SN_PARTS[1]}"
    TIMESTAMP_PART="${SN_PARTS[2]}"
    
    echo "  随机16进制部分: $RANDOM_HEX (长度: ${#RANDOM_HEX})"
    echo "  PCB SN部分: $PCB_PART"
    echo "  时间戳部分: $TIMESTAMP_PART (长度: ${#TIMESTAMP_PART})"
    
    # 验证随机16进制部分
    if [ ${#RANDOM_HEX} -eq 8 ]; then
        echo "  ✅ 随机16进制部分长度正确"
    else
        echo "  ❌ 随机16进制部分长度错误"
    fi
    
    # 验证PCB SN部分
    if [ "$PCB_PART" = "$PCB_SN_1" ]; then
        echo "  ✅ PCB SN部分匹配"
    else
        echo "  ❌ PCB SN部分不匹配"
    fi
    
    # 验证时间戳部分
    if [ ${#TIMESTAMP_PART} -ge 13 ]; then
        echo "  ✅ 时间戳部分长度正确"
        # 转换时间戳为可读时间
        READABLE_TIME=$(date -d "@$((TIMESTAMP_PART/1000))" 2>/dev/null || echo "无法解析")
        echo "  时间戳对应时间: $READABLE_TIME"
    else
        echo "  ❌ 时间戳部分长度错误"
    fi
else
    echo "  ❌ SN号格式错误，应该包含3个部分"
fi
echo ""

# 4. 测试申请多个复合SN号
echo "4. 测试申请多个复合SN号..."
PCB_SN_2="PCB987654321"
APPLY_RESPONSE_2=$(curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN_2\",
    \"device_type\": \"toilet_v2\",
    \"production_line\": \"line_002\", 
    \"batch_number\": \"batch_composite_002\",
    \"operator\": \"复合SN测试员\",
    \"quantity\": 3,
    \"remark\": \"复合SN号测试 - 多个\"
  }")

echo "$APPLY_RESPONSE_2" | jq '.'

# 验证多个SN号的唯一性
SN_LIST=$(echo "$APPLY_RESPONSE_2" | jq -r '.data.sn_list[]')
echo "生成的多个SN号:"
for sn in $SN_LIST; do
    echo "  $sn"
done
echo ""

# 5. 测试获取SN详细信息
echo "5. 测试获取复合SN详细信息..."
curl -s -X GET "$API_BASE/sn/$FIRST_SN" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 6. 测试按PCB SN查询
echo "6. 测试按PCB SN查询..."
curl -s -X GET "$API_BASE/sn/query?pcb_sn=$PCB_SN_1&page=1&page_size=10" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

# 7. 测试更新复合SN状态
echo "7. 测试更新复合SN状态..."
curl -s -X PUT "$API_BASE/sn/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"sn\": \"$FIRST_SN\",
    \"status\": 2,
    \"operator\": \"复合SN测试员\",
    \"remark\": \"复合SN测试使用\"
  }" | jq '.'
echo ""

# 8. 测试错误情况 - 缺少PCB SN
echo "8. 测试错误情况 - 缺少PCB SN..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d '{
    "device_type": "toilet_v1",
    "quantity": 1
  }' | jq '.'
echo ""

# 9. 测试错误情况 - 空PCB SN
echo "9. 测试错误情况 - 空PCB SN..."
curl -s -X POST "$API_BASE/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d '{
    "pcb_sn": "",
    "device_type": "toilet_v1",
    "quantity": 1
  }' | jq '.'
echo ""

# 10. 测试统计信息
echo "10. 测试统计信息..."
curl -s -X GET "$API_BASE/statistics" \
  -H "Authorization: Bearer $FACTORY_TOKEN" | jq '.'
echo ""

echo "=================================================="
echo "复合SN号测试完成!"
echo ""
echo "📝 复合SN号格式说明:"
echo "格式: 8位随机16进制_PCB_SN_时间戳"
echo "示例: A1B2C3D4_PCB001234567_1737012345678"
echo ""
echo "🔍 各部分说明:"
echo "1. 8位随机16进制: 每次生成都不同，确保唯一性"
echo "2. PCB SN: 用户提供的PCB板序列号"
echo "3. 时间戳: 生成时的毫秒级时间戳"
echo ""
echo "✅ 测试验证项目:"
echo "- SN号格式正确性"
echo "- 各部分长度验证"
echo "- PCB SN匹配验证"
echo "- 时间戳有效性验证"
echo "- 多个SN号唯一性"
echo "- 数据库存储完整性"
