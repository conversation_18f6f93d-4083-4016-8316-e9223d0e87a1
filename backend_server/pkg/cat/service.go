package cat

import (
	"cabycare-server/config"
	"crypto/sha256"
	"fmt"
	"log"
	"time"
)

type CatService struct {
	db *Database
}

func NewCatService(cfg *config.Config) (*CatService, error) {
	db, err := NewDatabase(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create cat database: %v", err)
	}

	return &CatService{
		db: db,
	}, nil
}

// CreateCat 创建新猫咪
func (s *CatService) CreateCat(cat *Cat) error {
	return s.db.CreateCat(cat)
}

// GetCat 获取猫咪信息
func (s *CatService) GetCat(catID string) (*Cat, error) {
	return s.db.GetCat(catID)
}

// UpdateCat 更新猫咪信息
func (s *CatService) UpdateCat(cat *Cat) error {
	return s.db.UpdateCat(cat)
}

// ListUserCats 获取用户的所有猫咪
func (s *CatService) ListUserCats(userID string) ([]Cat, error) {
	return s.db.ListUserCats(userID)
}

// CreateCatBehavior 创建猫咪行为特征
func (s *CatService) CreateCatBehavior(behavior *CatBehavior) error {
	return s.db.CreateCatBehavior(behavior)
}

// GetCatBehavior 获取猫咪行为特征
func (s *CatService) GetCatBehavior(catID string) (*CatBehavior, error) {
	return s.db.GetCatBehavior(catID)
}

// UpdateCatBehavior 更新猫咪行为特征
func (s *CatService) UpdateCatBehavior(behavior *CatBehavior) error {
	return s.db.UpdateCatBehavior(behavior)
}

// CreateCatMetricsDaily 创建猫咪每日统计
func (s *CatService) CreateCatMetricsDaily(metrics *CatMetricsDaily) error {
	return s.db.CreateCatMetricsDaily(metrics)
}

// GetCatDailyMetrics 获取猫咪每日统计
func (s *CatService) GetCatDailyMetrics(catID string, date time.Time) (*CatMetricsDaily, error) {
	return s.db.GetCatDailyMetrics(catID, date)
}

// UpdateCatMetricsDaily 更新猫咪每日统计
func (s *CatService) UpdateCatMetricsDaily(metrics *CatMetricsDaily) error {
	return s.db.UpdateCatMetricsDaily(metrics)
}

// CreateCatMetricsMonthly 创建猫咪月度统计
func (s *CatService) CreateCatMetricsMonthly(metrics *CatMetricsMonthly) error {
	return s.db.CreateCatMetricsMonthly(metrics)
}

// GetCatMonthlyMetrics 获取猫咪月度统计
func (s *CatService) GetCatMonthlyMetrics(catID string, year, month int) (*CatMetricsMonthly, error) {
	return s.db.GetCatMonthlyMetrics(catID, year, month)
}

// UpdateCatMetricsMonthly 更新猫咪月度统计
func (s *CatService) UpdateCatMetricsMonthly(metrics *CatMetricsMonthly) error {
	return s.db.UpdateCatMetricsMonthly(metrics)
}

// CreateCatAlert 创建猫咪健康警报
func (s *CatService) CreateCatAlert(alert *CatAlert) error {
	return s.db.CreateCatAlert(alert)
}

// ListCatAlerts 获取猫咪健康警报
func (s *CatService) ListCatAlerts(catID string, status int8) ([]CatAlert, error) {
	return s.db.ListCatAlerts(catID, status)
}

// UpdateCatAlert 更新猫咪健康警报
func (s *CatService) UpdateCatAlert(alert *CatAlert) error {
	return s.db.UpdateCatAlert(alert)
}

// CreateDefaultUnknownCat 为用户创建默认的 unknown 猫咪
func (s *CatService) CreateDefaultUnknownCat(userID string) error {
	// 生成一个基于用户ID的固定cat_id
	hasher := sha256.New()
	hasher.Write([]byte(userID + "_unknown"))
	catID := fmt.Sprintf("c_%x", hasher.Sum(nil)[:8])

	// 创建默认猫咪
	cat := &Cat{
		CatID:     catID,
		UserID:    userID,
		Name:      "Unknown",
		Status:    1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.db.CreateCat(cat); err != nil {
		return fmt.Errorf("failed to create unknown cat: %v", err)
	}

	log.Printf("Created unknown cat with ID: %s for user: %s", catID, userID)
	return nil
}

// CreateUnknownCat 创建一个未知猫咪及其档案
func (s *CatService) CreateUnknownCat(catID, userID string) error {
	// 创建未知猫咪
	cat := &Cat{
		CatID:     catID,
		UserID:    userID,
		Name:      "You Know Who",
		Birthday:  time.Now(),
		Gender:    0, // 未知性别
		Breed:     "Unknown",
		Color:     "Unknown",
		Status:    1, // 正常状态
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if err := s.db.CreateCat(cat); err != nil {
		return fmt.Errorf("failed to create unknown cat: %v", err)
	}

	return nil
}

// DeleteCat 软删除猫咪
func (s *CatService) DeleteCat(catID string, userID string) error {
	return s.db.DeleteCat(catID, userID)
}

// HideCat 隐藏猫咪
func (s *CatService) HideCat(catID string, userID string) error {
	return s.db.HideCat(catID, userID)
}

// RestoreCat 恢复猫咪（取消隐藏）
func (s *CatService) RestoreCat(catID string, userID string) error {
	return s.db.RestoreCat(catID, userID)
}

// ListUserHiddenCats 获取用户隐藏的猫咪列表
func (s *CatService) ListUserHiddenCats(userID string) ([]Cat, error) {
	return s.db.ListUserHiddenCats(userID)
}

// ListUserAllCats 获取用户所有猫咪（包括隐藏的，不包括已删除的）
func (s *CatService) ListUserAllCats(userID string) ([]Cat, error) {
	return s.db.ListUserAllCats(userID)
}