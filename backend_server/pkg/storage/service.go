package storage

import (
	"bytes"
	"cabycare-server/config"
	"context"
	"encoding/base64"
	"fmt"
	"image"
	_ "image/gif"
	"image/jpeg"
	_ "image/png"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/nfnt/resize"
)

// 存储桶和路径常量
const (
	// 桶名称
	BucketRecords = "records"
	BucketAssets  = "assets"
	
	// 路径前缀
	DevicePrefix    = "device"
	AvatarCatsPath  = "avatars/cats"
	AvatarUsersPath = "avatars/users"
)

type StorageService struct {
	minioClient *minio.Client
	config      *config.Config
}

// NewStorageService 创建视频服务实例
func NewStorageService(cfg *config.Config) (*StorageService, error) {
	// 初始化 MinIO 客户端
	minioClient, err := minio.New(cfg.Minio.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.Minio.AccessKey, cfg.Minio.SecretKey, ""),
		Secure: cfg.Minio.UseSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %v", err)
	}

	return &StorageService{
		minioClient: minioClient,
		config:      cfg,
	}, nil
}

// GetMinioClient 获取 MinIO 客户端
func (s *StorageService) GetMinioClient() *minio.Client {
	return s.minioClient
}

// CheckBucketExists 检查 bucket 是否存在
func (s *StorageService) CheckBucketExists(bucketName string) error {
	ctx := context.Background()
	exists, err := s.minioClient.BucketExists(ctx, bucketName)
	if err != nil {
		return fmt.Errorf("failed to check bucket: %v", err)
	}
	if !exists {
		return fmt.Errorf("bucket %s does not exist", bucketName)
	}
	return nil
}

// RemovePathFromBucket 删除 bucket 中的路径
func (s *StorageService) RemovePathFromBucket(bucketName, objectPath string) error {
	ctx := context.Background()
	err := s.minioClient.RemoveObject(ctx, bucketName, objectPath, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to remove object from bucket: %v", err)
	}
	return nil
}

// DeleteObject 删除指定路径的对象
func (s *StorageService) DeleteObject(path string) error {
	// 1. 解析路径获取bucket和object key
	parts := strings.SplitN(path, "/", 2)
	if len(parts) != 2 {
		return fmt.Errorf("invalid path format: %s", path)
	}
	bucket := parts[0]
	objectKey := parts[1]

	// 2. 删除对象
	ctx := context.Background()
	err := s.minioClient.RemoveObject(ctx, bucket, objectKey, minio.RemoveObjectOptions{
		ForceDelete: true,
	})
	if err != nil {
		return fmt.Errorf("failed to delete object: %v", err)
	}

	return nil
}

// ListObjects 列出指定前缀的所有对象
func (s *StorageService) ListObjects(prefix string) ([]string, error) {
	// 1. 解析前缀获取bucket和object前缀
	parts := strings.SplitN(prefix, "/", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid prefix format: %s", prefix)
	}
	bucket := parts[0]
	objectPrefix := parts[1]

	// 2. 列出对象
	ctx := context.Background()
	objectCh := s.minioClient.ListObjects(ctx, bucket, minio.ListObjectsOptions{
		Prefix:    objectPrefix,
		Recursive: true,
	})

	var objects []string
	for object := range objectCh {
		if object.Err != nil {
			return nil, fmt.Errorf("error listing objects: %v", object.Err)
		}
		objects = append(objects, object.Key)
	}

	return objects, nil
}

// GetObjectContent 获取指定对象的内容
func (s *StorageService) GetObjectContent(path string) ([]byte, error) {
	// 1. 解析路径获取bucket和object key
	parts := strings.SplitN(path, "/", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid path format: %s", path)
	}
	bucket := parts[0]
	objectKey := parts[1]

	// 2. 获取对象
	ctx := context.Background()
	obj, err := s.minioClient.GetObject(ctx, bucket, objectKey, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %v", err)
	}
	defer obj.Close()

	// 3. 读取对象内容
	stat, err := obj.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get object stats: %v", err)
	}

	buffer := make([]byte, stat.Size)
	_, err = obj.Read(buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to read object content: %v", err)
	}

	return buffer, nil
}

// GetObject 获取对象，返回io.ReadCloser接口
func (s *StorageService) GetObject(path string) (*minio.Object, error) {
	// 1. 解析路径获取bucket和object key
	parts := strings.SplitN(path, "/", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid path format: %s", path)
	}
	bucket := parts[0]
	objectKey := parts[1]

	// 2. 获取对象
	ctx := context.Background()
	obj, err := s.minioClient.GetObject(ctx, bucket, objectKey, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %v", err)
	}

	// 确保对象存在
	_, err = obj.Stat()
	if err != nil {
		obj.Close()
		return nil, fmt.Errorf("failed to get object stats: %v", err)
	}

	return obj, nil
}

// GetVideoPath without duration - 使用统一的GenerateVideoURL方法
func (s *StorageService) GetVideoPath(loc *time.Location, startTime int64, deviceID string) (string, error) {
	startTimeObj := time.Unix(startTime, 0).In(loc)
	return s.GenerateVideoURL(startTimeObj, deviceID, nil), nil
}

// GetVideoPath with duration - 使用统一的GenerateVideoURL方法
func (s *StorageService) GetVideoPathWithDuration(loc *time.Location, startTime int64, duration float64, deviceID string) (string, error) {
	startTimeObj := time.Unix(startTime, 0).In(loc)
	return s.GenerateVideoURL(startTimeObj, deviceID, &duration), nil
}

// UploadCatAvatar 上传猫咪头像，将base64图像裁剪成方形并存储到assets/cats目录
func (s *StorageService) UploadCatAvatar(catID string, base64Image string) (string, error) {
	// 1. 解码base64图像
	imageData, err := base64.StdEncoding.DecodeString(base64Image)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 image: %v", err)
	}

	// 2. 解析图像
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return "", fmt.Errorf("failed to decode image: %v", err)
	}

	// 3. 裁剪成方形
	squareImg := s.cropToSquare(img)

	// 4. 调整大小为512x512
	resizedImg := resize.Resize(512, 512, squareImg, resize.Lanczos3)

	// 5. 编码为JPEG
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, resizedImg, &jpeg.Options{Quality: 90})
	if err != nil {
		return "", fmt.Errorf("failed to encode image to JPEG: %v", err)
	}

	// 6. 上传到MinIO
	objectPath := fmt.Sprintf("%s/%s.jpg", AvatarCatsPath, catID)
	ctx := context.Background()
	
	_, err = s.minioClient.PutObject(ctx, BucketAssets, objectPath, bytes.NewReader(buf.Bytes()), int64(buf.Len()), minio.PutObjectOptions{
		ContentType: "image/jpeg",
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload image to MinIO: %v", err)
	}

	// 7. 返回相对路径，稍后在API中组装完整URL
	return objectPath, nil
}

// cropToSquare 将图像裁剪成方形（取较小的边长）
func (s *StorageService) cropToSquare(img image.Image) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 取较小的边长作为正方形的边长
	size := width
	if height < width {
		size = height
	}

	// 计算裁剪起始点（居中裁剪）
	x := (width - size) / 2
	y := (height - size) / 2

	// 创建裁剪后的图像
	rect := image.Rect(0, 0, size, size)
	squareImg := image.NewRGBA(rect)

	// 复制像素
	for dy := 0; dy < size; dy++ {
		for dx := 0; dx < size; dx++ {
			squareImg.Set(dx, dy, img.At(x+dx, y+dy))
		}
	}

	return squareImg
}

// GetCatAvatarURL 获取猫咪头像URL
func (s *StorageService) GetCatAvatarURL(catID string) string {
	return fmt.Sprintf("%s/api/storage/assets/avatars/cats/%s.jpg", s.config.CabyBackend.URL, catID)
}

// DeleteCatAvatar 删除猫咪头像
func (s *StorageService) DeleteCatAvatar(catID string) error {
	objectPath := fmt.Sprintf("%s/%s.jpg", AvatarCatsPath, catID)
	return s.RemovePathFromBucket(BucketAssets, objectPath)
}

// ==================== 统一的 MinIO 文件获取方法 ====================

// ParseDeviceIDFromPath 从路径中解析设备ID
// 支持新格式：records/device{deviceID} 和旧格式：device{deviceID}
func (s *StorageService) ParseDeviceIDFromPath(path string) (string, error) {
	// 新格式：records/device{deviceID}
	if strings.HasPrefix(path, BucketRecords+"/"+DevicePrefix) {
		deviceID := strings.TrimPrefix(path, BucketRecords+"/"+DevicePrefix)
		if deviceID == "" {
			return "", fmt.Errorf("invalid path format: %s", path)
		}
		return deviceID, nil
	}
	
	// 旧格式兼容：device{deviceID}（直接桶名）
	if strings.HasPrefix(path, DevicePrefix) {
		deviceID := strings.TrimPrefix(path, DevicePrefix)
		if deviceID == "" {
			return "", fmt.Errorf("invalid path format: %s", path)
		}
		return deviceID, nil
	}
	
	return "", fmt.Errorf("unsupported path format: %s", path)
}

// GetDevicePathPrefix 获取设备路径前缀（在records桶中）
func (s *StorageService) GetDevicePathPrefix(deviceID string) string {
	return fmt.Sprintf("%s%s", DevicePrefix, deviceID)
}

// GetMinioObjectFromBucket 从指定桶获取MinIO对象
func (s *StorageService) GetMinioObjectFromBucket(ctx context.Context, bucket, objectPath string) (*minio.Object, error) {
	obj, err := s.minioClient.GetObject(ctx, bucket, objectPath, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get object from bucket %s: %v", bucket, err)
	}
	
	// 验证对象是否存在
	_, err = obj.Stat()
	if err != nil {
		obj.Close()
		return nil, fmt.Errorf("failed to get object stats: %v", err)
	}
	
	return obj, nil
}

// GetPlaylistContent 获取HLS播放列表内容（从records桶中）
func (s *StorageService) GetPlaylistContent(ctx context.Context, deviceID string, timestamp time.Time) ([]byte, error) {
	// 构建在records桶中的对象路径：device{deviceID}/2006-01-02_15-04-05_hls/playlist.m3u8
	devicePath := s.GetDevicePathPrefix(deviceID)
	folderName := timestamp.Format("2006-01-02_15-04-05") + "_hls"
	objectPath := fmt.Sprintf("%s/%s/playlist.m3u8", devicePath, folderName)
	
	obj, err := s.GetMinioObjectFromBucket(ctx, BucketRecords, objectPath)
	if err != nil {
		return nil, err
	}
	defer obj.Close()
	
	content, err := io.ReadAll(obj)
	if err != nil {
		return nil, fmt.Errorf("failed to read playlist content: %v", err)
	}
	
	return content, nil
}

// GetVideoSegmentFromRecords 从records桶获取视频片段
func (s *StorageService) GetVideoSegmentFromRecords(ctx context.Context, deviceID, folder, filename string) (*minio.Object, error) {
	// 构建在records桶中的对象路径：device{deviceID}/folder/filename
	devicePath := s.GetDevicePathPrefix(deviceID)
	objectPath := fmt.Sprintf("%s/%s/%s", devicePath, folder, filename)
	return s.GetMinioObjectFromBucket(ctx, BucketRecords, objectPath)
}

// GetVideoSegment 从指定桶获取视频片段（保持向后兼容）
func (s *StorageService) GetVideoSegment(ctx context.Context, bucket, folder, filename string) (*minio.Object, error) {
	objectPath := fmt.Sprintf("%s/%s", folder, filename)
	return s.GetMinioObjectFromBucket(ctx, bucket, objectPath)
}

// GetThumbnail 获取缩略图（从records桶中）
func (s *StorageService) GetThumbnail(ctx context.Context, deviceID string, timestamp time.Time) (*minio.Object, error) {
	// 构建在records桶中的对象路径：device{deviceID}/2006-01-02_15-04-05_hls/cover.jpg
	devicePath := s.GetDevicePathPrefix(deviceID)
	thumbnailFolder := timestamp.Format("2006-01-02_15-04-05") + "_hls"
	objectPath := fmt.Sprintf("%s/%s/cover.jpg", devicePath, thumbnailFolder)
	
	return s.GetMinioObjectFromBucket(ctx, BucketRecords, objectPath)
}

// GenerateThumbnailURL 生成缩略图URL
func (s *StorageService) GenerateThumbnailURL(timestamp time.Time, deviceID, scheme, host string) string {
	// 生成编码的时间字符串，使用原始时间戳格式
	timeStr := timestamp.Format("2006-01-02T15:04:05+08:00")
	encodedTime := url.QueryEscape(timeStr)
	
	// 构建缩略图URL - 使用新的路径格式：records/device{deviceID}
	devicePath := fmt.Sprintf("%s/%s", BucketRecords, s.GetDevicePathPrefix(deviceID))
	
	thumbnailURL := fmt.Sprintf("%s://%s/api/records/videos/thumbnail/%s?bucket=%s",
		scheme, host, encodedTime, devicePath)
	
	return thumbnailURL
}

// GenerateVideoURL 生成视频URL（统一方法）
func (s *StorageService) GenerateVideoURL(timestamp time.Time, deviceID string, duration *float64) string {
	timeStr := timestamp.Format(time.RFC3339)
	encodedStart := url.QueryEscape(timeStr)
	bucketPath := fmt.Sprintf("%s/%s%s", BucketRecords, DevicePrefix, deviceID)
	
	videoURL := fmt.Sprintf("%s/api/records/videos/get?path=%s&start=%s",
		s.config.CabyBackend.URL,
		bucketPath,
		encodedStart,
	)
	
	if duration != nil {
		durationStr := fmt.Sprintf("%.3f", *duration)
		videoURL = fmt.Sprintf("%s&duration=%s", videoURL, durationStr)
	}
	
	return videoURL
}
