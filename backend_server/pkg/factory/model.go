package factory

import (
	"time"
)

// SNRecord SN号记录模型
type SNRecord struct {
	ID             uint64     `json:"id" gorm:"primaryKey;autoIncrement"`
	SN             string     `json:"sn" gorm:"type:varchar(12);uniqueIndex;not null"`
	Status         int8       `json:"status" gorm:"type:tinyint;not null;default:1;index"` // 1-已申请未使用, 2-已使用, 3-已废弃
	DeviceType     *string    `json:"device_type" gorm:"type:varchar(32);index"`
	ProductionLine *string    `json:"production_line" gorm:"type:varchar(32);index"`
	BatchNumber    *string    `json:"batch_number" gorm:"type:varchar(32);index"`
	Operator       *string    `json:"operator" gorm:"type:varchar(64)"`
	AppliedAt      time.Time  `json:"applied_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;index"`
	UsedAt         *time.Time `json:"used_at" gorm:"type:timestamp;index"`
	CreatedAt      time.Time  `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt      time.Time  `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	Remark         *string    `json:"remark" gorm:"type:text"`
}

// TableName 指定表名
func (SNRecord) TableName() string {
	return "sn_records"
}

// SNSequence SN号序列模型
type SNSequence struct {
	ID            uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Prefix        string    `json:"prefix" gorm:"type:varchar(4);uniqueIndex;not null;default:''"`
	CurrentNumber uint64    `json:"current_number" gorm:"type:bigint;not null;default:0"`
	MaxNumber     uint64    `json:"max_number" gorm:"type:bigint;not null;default:99999999"`
	CreatedAt     time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (SNSequence) TableName() string {
	return "sn_sequence"
}

// ProductionBatch 生产批次模型
type ProductionBatch struct {
	ID              uint64     `json:"id" gorm:"primaryKey;autoIncrement"`
	BatchNumber     string     `json:"batch_number" gorm:"type:varchar(32);uniqueIndex;not null"`
	DeviceType      string     `json:"device_type" gorm:"type:varchar(32);not null;index"`
	ProductionLine  string     `json:"production_line" gorm:"type:varchar(32);not null;index"`
	PlannedQuantity int        `json:"planned_quantity" gorm:"type:int;not null;default:0"`
	ActualQuantity  int        `json:"actual_quantity" gorm:"type:int;not null;default:0"`
	StartTime       *time.Time `json:"start_time" gorm:"type:timestamp;index"`
	EndTime         *time.Time `json:"end_time" gorm:"type:timestamp"`
	Status          int8       `json:"status" gorm:"type:tinyint;not null;default:1;index"` // 1-计划中, 2-生产中, 3-已完成, 4-已取消
	Operator        *string    `json:"operator" gorm:"type:varchar(64)"`
	CreatedAt       time.Time  `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt       time.Time  `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	Remark          *string    `json:"remark" gorm:"type:text"`
}

// TableName 指定表名
func (ProductionBatch) TableName() string {
	return "production_batches"
}

// SNApplyLog SN号申请日志模型
type SNApplyLog struct {
	ID             uint64    `json:"id" gorm:"primaryKey;autoIncrement"`
	SN             string    `json:"sn" gorm:"type:varchar(12);not null;index"`
	Action         string    `json:"action" gorm:"type:varchar(32);not null;index"` // apply-申请, use-使用, discard-废弃
	Operator       *string   `json:"operator" gorm:"type:varchar(64);index"`
	IPAddress      *string   `json:"ip_address" gorm:"type:varchar(45)"`
	UserAgent      *string   `json:"user_agent" gorm:"type:varchar(255)"`
	BatchNumber    *string   `json:"batch_number" gorm:"type:varchar(32);index"`
	DeviceType     *string   `json:"device_type" gorm:"type:varchar(32)"`
	ProductionLine *string   `json:"production_line" gorm:"type:varchar(32)"`
	CreatedAt      time.Time `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;index"`
	Remark         *string   `json:"remark" gorm:"type:text"`
}

// TableName 指定表名
func (SNApplyLog) TableName() string {
	return "sn_apply_logs"
}

// SN状态常量
const (
	SNStatusApplied  = 1 // 已申请未使用
	SNStatusUsed     = 2 // 已使用
	SNStatusDiscarded = 3 // 已废弃
)

// 生产批次状态常量
const (
	BatchStatusPlanned    = 1 // 计划中
	BatchStatusProducing  = 2 // 生产中
	BatchStatusCompleted  = 3 // 已完成
	BatchStatusCancelled  = 4 // 已取消
)

// 操作类型常量
const (
	ActionApply   = "apply"   // 申请
	ActionUse     = "use"     // 使用
	ActionDiscard = "discard" // 废弃
)

// SNApplyRequest SN号申请请求
type SNApplyRequest struct {
	DeviceType     *string `json:"device_type" binding:"omitempty,max=32"`
	ProductionLine *string `json:"production_line" binding:"omitempty,max=32"`
	BatchNumber    *string `json:"batch_number" binding:"omitempty,max=32"`
	Operator       *string `json:"operator" binding:"omitempty,max=64"`
	Quantity       int     `json:"quantity" binding:"required,min=1,max=100"` // 申请数量，限制最多100个
	Remark         *string `json:"remark" binding:"omitempty,max=500"`
}

// SNApplyResponse SN号申请响应
type SNApplyResponse struct {
	SNList    []string `json:"sn_list"`
	Count     int      `json:"count"`
	RequestID string   `json:"request_id"`
}

// SNQueryRequest SN号查询请求
type SNQueryRequest struct {
	SN             *string `json:"sn" form:"sn" binding:"omitempty,len=12"`
	Status         *int8   `json:"status" form:"status" binding:"omitempty,oneof=1 2 3"`
	DeviceType     *string `json:"device_type" form:"device_type" binding:"omitempty,max=32"`
	ProductionLine *string `json:"production_line" form:"production_line" binding:"omitempty,max=32"`
	BatchNumber    *string `json:"batch_number" form:"batch_number" binding:"omitempty,max=32"`
	Operator       *string `json:"operator" form:"operator" binding:"omitempty,max=64"`
	StartTime      *string `json:"start_time" form:"start_time" binding:"omitempty"`
	EndTime        *string `json:"end_time" form:"end_time" binding:"omitempty"`
	Page           int     `json:"page" form:"page" binding:"omitempty,min=1"`
	PageSize       int     `json:"page_size" form:"page_size" binding:"omitempty,min=1,max=100"`
}

// SNQueryResponse SN号查询响应
type SNQueryResponse struct {
	Records []SNRecord `json:"records"`
	Total   int64      `json:"total"`
	Page    int        `json:"page"`
	PageSize int       `json:"page_size"`
}

// SNUpdateRequest SN号状态更新请求
type SNUpdateRequest struct {
	SN      string  `json:"sn" binding:"required,len=12"`
	Status  int8    `json:"status" binding:"required,oneof=2 3"` // 只能更新为已使用或已废弃
	Operator *string `json:"operator" binding:"omitempty,max=64"`
	Remark  *string `json:"remark" binding:"omitempty,max=500"`
}
