package factory

import (
	"cabycare-server/config"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Service 工厂服务结构体
type Service struct {
	cfg *config.Config
	db  *Database
}

// NewService 创建新的工厂服务实例
func NewService(cfg *config.Config) (*Service, error) {
	// 连接工厂数据库
	factoryDB, err := cfg.GetFactoryDB()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to factory database: %v", err)
	}

	// 创建数据库操作实例
	db := NewDatabase(factoryDB)

	return &Service{
		cfg: cfg,
		db:  db,
	}, nil
}

// ApplySN 申请SN号
func (s *Service) ApplySN(req *SNApplyRequest, clientIP, userAgent string) (*SNApplyResponse, error) {
	if req.Quantity <= 0 || req.Quantity > 100 {
		return nil, fmt.<PERSON><PERSON><PERSON>("invalid quantity: must be between 1 and 100")
	}

	if req.PCBSN == "" {
		return nil, fmt.Errorf("PCB SN is required")
	}

	var snList []string
	var records []SNRecord
	var logs []SNApplyLog
	requestID := uuid.New().String()

	// 生成SN号
	for i := 0; i < req.Quantity; i++ {
		// 生成复合SN号
		compositeSN, randomHex, timestamp, err := s.db.GenerateCompositeSN(req.PCBSN)
		if err != nil {
			return nil, fmt.Errorf("failed to generate composite SN: %v", err)
		}

		// 检查SN是否已存在（双重保险）
		exists, err := s.db.CheckSNExists(compositeSN)
		if err != nil {
			return nil, fmt.Errorf("failed to check SN existence: %v", err)
		}
		if exists {
			// 如果存在冲突，重新生成
			compositeSN, randomHex, timestamp, err = s.db.GenerateCompositeSN(req.PCBSN)
			if err != nil {
				return nil, fmt.Errorf("failed to regenerate composite SN: %v", err)
			}
		}

		snList = append(snList, compositeSN)

		// 创建SN记录
		record := SNRecord{
			SN:             compositeSN,
			RandomHex:      randomHex,
			PCBSN:          req.PCBSN,
			Timestamp:      timestamp,
			Status:         SNStatusApplied,
			DeviceType:     req.DeviceType,
			ProductionLine: req.ProductionLine,
			BatchNumber:    req.BatchNumber,
			Operator:       req.Operator,
			AppliedAt:      time.Now(),
			Remark:         req.Remark,
		}
		records = append(records, record)

		// 创建申请日志
		logEntry := SNApplyLog{
			SN:             compositeSN,
			Action:         ActionApply,
			Operator:       req.Operator,
			IPAddress:      &clientIP,
			UserAgent:      &userAgent,
			BatchNumber:    req.BatchNumber,
			DeviceType:     req.DeviceType,
			ProductionLine: req.ProductionLine,
			Remark:         req.Remark,
		}
		logs = append(logs, logEntry)
	}

	// 批量保存记录
	if err := s.db.BatchCreateSNRecords(records); err != nil {
		return nil, fmt.Errorf("failed to create SN records: %v", err)
	}

	// 批量保存日志
	if err := s.db.BatchCreateSNApplyLogs(logs); err != nil {
		log.Printf("Failed to create SN apply logs: %v", err)
		// 日志保存失败不影响主流程
	}

	return &SNApplyResponse{
		SNList:    snList,
		Count:     len(snList),
		RequestID: requestID,
	}, nil
}

// QuerySN 查询SN号
func (s *Service) QuerySN(req *SNQueryRequest) (*SNQueryResponse, error) {
	records, total, err := s.db.QuerySNRecords(req)
	if err != nil {
		return nil, fmt.Errorf("failed to query SN records: %v", err)
	}

	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}

	return &SNQueryResponse{
		Records:  records,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// UpdateSNStatus 更新SN状态
func (s *Service) UpdateSNStatus(req *SNUpdateRequest, clientIP, userAgent string) error {
	// 获取现有记录
	record, err := s.db.GetSNRecord(req.SN)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("SN %s not found", req.SN)
		}
		return fmt.Errorf("failed to get SN record: %v", err)
	}

	// 检查状态转换是否合法
	if record.Status == SNStatusUsed && req.Status == SNStatusDiscarded {
		return fmt.Errorf("cannot discard a used SN")
	}
	if record.Status == SNStatusDiscarded {
		return fmt.Errorf("cannot update a discarded SN")
	}

	// 更新记录
	oldStatus := record.Status
	record.Status = req.Status
	if req.Status == SNStatusUsed {
		now := time.Now()
		record.UsedAt = &now
	}
	if req.Remark != nil {
		record.Remark = req.Remark
	}

	if err := s.db.UpdateSNRecord(record); err != nil {
		return fmt.Errorf("failed to update SN record: %v", err)
	}

	// 创建操作日志
	action := ActionUse
	if req.Status == SNStatusDiscarded {
		action = ActionDiscard
	}

	logEntry := SNApplyLog{
		SN:        req.SN,
		Action:    action,
		Operator:  req.Operator,
		IPAddress: &clientIP,
		UserAgent: &userAgent,
		Remark:    req.Remark,
	}

	if err := s.db.CreateSNApplyLog(&logEntry); err != nil {
		log.Printf("Failed to create SN update log: %v", err)
		// 日志保存失败不影响主流程
	}

	log.Printf("SN %s status updated from %d to %d by %v", req.SN, oldStatus, req.Status, req.Operator)
	return nil
}

// GetSNInfo 获取SN详细信息
func (s *Service) GetSNInfo(sn string) (*SNRecord, error) {
	record, err := s.db.GetSNRecord(sn)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("SN %s not found", sn)
		}
		return nil, fmt.Errorf("failed to get SN record: %v", err)
	}
	return record, nil
}

// GetStatistics 获取统计信息
func (s *Service) GetStatistics() (map[string]interface{}, error) {
	return s.db.GetSNStatistics()
}

// ValidateSN 验证复合SN号格式
func (s *Service) ValidateSN(sn string) error {
	if sn == "" {
		return fmt.Errorf("SN cannot be empty")
	}

	// 复合SN号格式：8位16进制_PCB_SN_时间戳
	// 最小长度检查（8位16进制 + _ + 至少1位PCB + _ + 至少13位时间戳）
	if len(sn) < 23 {
		return fmt.Errorf("SN format invalid: too short")
	}

	// 检查是否包含两个下划线分隔符
	parts := strings.Split(sn, "_")
	if len(parts) != 3 {
		return fmt.Errorf("SN format invalid: must be RANDOM_HEX_PCB_SN_TIMESTAMP")
	}

	// 验证第一部分：8位16进制
	randomHex := parts[0]
	if len(randomHex) != 8 {
		return fmt.Errorf("SN format invalid: random hex part must be 8 characters")
	}
	for _, char := range randomHex {
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
			return fmt.Errorf("SN format invalid: random hex part must contain only hexadecimal characters")
		}
	}

	// 验证第二部分：PCB SN（非空即可）
	pcbSN := parts[1]
	if pcbSN == "" {
		return fmt.Errorf("SN format invalid: PCB SN part cannot be empty")
	}

	// 验证第三部分：时间戳（必须是数字）
	timestampStr := parts[2]
	if len(timestampStr) < 13 {
		return fmt.Errorf("SN format invalid: timestamp part too short")
	}
	for _, char := range timestampStr {
		if char < '0' || char > '9' {
			return fmt.Errorf("SN format invalid: timestamp part must contain only digits")
		}
	}

	return nil
}

// CheckSNExists 检查SN是否存在
func (s *Service) CheckSNExists(sn string) (bool, error) {
	return s.db.CheckSNExists(sn)
}
