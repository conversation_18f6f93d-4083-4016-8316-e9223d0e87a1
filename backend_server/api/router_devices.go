package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

func RegisterDevicesRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	devices := routerGroup.Group("/devices")
	{
		devices.POST("/register", catHandler.RegisterDevice)
		devices.POST("/heartbeat", catHandler.DeviceHeartbeat)
		devices.POST("/timezone", catHandler.DeviceTimezone)
		devices.GET("/:device_id", catHandler.GetDevice)
		devices.GET("", catHandler.ListUserDevices)
		devices.GET("/:device_id/status", catHandler.GetDeviceStatus)
		devices.GET("/:device_id/statistics", catHandler.GetDeviceStatistics)
		devices.GET("/:device_id/config", catHandler.GetDeviceConfig)
		devices.PUT("/:device_id/config", catHandler.UpdateDeviceConfig)
		devices.GET("/:device_id/setting", catHandler.GetDeviceAutoOTASettings)
		devices.PUT("/:device_id/setting", catHandler.UpdateDeviceAutoOTASettings)

		// OTA状态相关路由
		devices.GET("/:device_id/ota-status", catHandler.GetDeviceOTAStatus)
		devices.PUT("/:device_id/ota-status", catHandler.UpdateDeviceOTAStatus)

		// 传感器状态相关路由
		devices.POST("/:device_id/sensor-errors", catHandler.ReportDeviceSensorError)
		devices.GET("/:device_id/sensor-status", catHandler.GetDeviceSensorStatus)
		devices.DELETE("/:device_id/sensor-errors/:sensor_type", catHandler.ClearDeviceSensorError)
		devices.GET("/sensor-errors", catHandler.ListDevicesWithSensorErrors)

		// user related routes
		devices.POST("", catHandler.CreateUserHardware)
		devices.GET("/users/:user_id", catHandler.ListUserHardware)
		devices.GET("/hardware/:hardware_sn",c atHandler.GetHardwareUser)
		devices.GET("/check", catHandler.CheckUserHardwareExists)
		devices.GET("/accessible", catHandler.ListUserAccessibleDevices)
	}
}
