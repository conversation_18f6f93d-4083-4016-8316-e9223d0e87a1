package api

import (
	"cabycare-server/pkg/factory"

	"github.com/gin-gonic/gin"
)

// RegisterFactoryRoutes 注册工厂相关路由
func RegisterFactoryRoutes(router *gin.RouterGroup, factoryHandler *factory.Handler) {
	factoryGroup := router.Group("/factory")
	{
		// SN号相关路由
		snGroup := factoryGroup.Group("/sn")
		{
			// 申请SN号
			snGroup.POST("/apply", factoryHandler.ApplySN)
			
			// 查询SN号
			snGroup.GET("/query", factoryHandler.QuerySN)
			
			// 获取SN详细信息
			snGroup.GET("/:sn", factoryHandler.GetSNInfo)
			
			// 检查SN是否存在
			snGroup.GET("/:sn/check", factoryHandler.CheckSN)
			
			// 更新SN状态
			snGroup.PUT("/update", factoryHandler.UpdateSNStatus)
		}
		
		// 统计信息路由
		factoryGroup.GET("/statistics", factoryHandler.GetStatistics)
	}
}

// RegisterFactoryPublicRoutes 注册工厂公开路由（无需认证）
func RegisterFactoryPublicRoutes(router *gin.RouterGroup, factoryHandler *factory.Handler) {
	factoryGroup := router.Group("/factory")
	{
		// 健康检查
		factoryGroup.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":  "ok",
				"service": "factory",
			})
		})
		
		// SN号相关公开路由
		snGroup := factoryGroup.Group("/sn")
		{
			// 申请SN号（工厂生产线调用）
			snGroup.POST("/apply", factoryHandler.ApplySN)
			
			// 检查SN是否存在（工厂生产线调用）
			snGroup.GET("/:sn/check", factoryHandler.CheckSN)
			
			// 更新SN状态（工厂生产线调用）
			snGroup.PUT("/update", factoryHandler.UpdateSNStatus)
		}
	}
}

// RegisterFactoryAuthRoutes 注册工厂认证路由（需要认证）
func RegisterFactoryAuthRoutes(router *gin.RouterGroup, factoryHandler *factory.Handler) {
	factoryGroup := router.Group("/factory")
	{
		// SN号相关认证路由
		snGroup := factoryGroup.Group("/sn")
		{
			// 查询SN号（管理员查询）
			snGroup.GET("/query", factoryHandler.QuerySN)
			
			// 获取SN详细信息（管理员查询）
			snGroup.GET("/:sn", factoryHandler.GetSNInfo)
		}
		
		// 统计信息路由（管理员查看）
		factoryGroup.GET("/statistics", factoryHandler.GetStatistics)
	}
}
