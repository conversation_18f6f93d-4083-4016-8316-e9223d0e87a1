package api

import (
	"cabycare-server/config"
	"cabycare-server/pkg/cattoilet"
	"cabycare-server/pkg/notification"
	"cabycare-server/pkg/video"

	"cabycare-server/pkg/auth"

	"github.com/gin-gonic/gin"
)

func SetupRouter(cfg *config.Config, videoHandler *video.Handler, catHandler *cattoilet.Handler,
	authHandler *auth.Auth<PERSON><PERSON>, catToiletService *cattoilet.CatToiletService,
	notificationHandler *notification.Handler) *gin.Engine {
	r := gin.Default()
	r.SetTrustedProxies([]string{"127.0.0.1"})
	r.TrustedPlatform = gin.PlatformCloudflare

	// add cors middleware
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// public routes
	public := r.Group("/api")
	{
		public.GET("/callback", authHandler.HandleCallback)
		public.POST("/refresh", authHandler.RefreshToken)
		public.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ok"})
		})

		RegisterRecordsRoutes(public, catHandler, videoHandler)
		RegisterDevicesRoutes(public, catHandler)
	}

	// user auth routes
	authorizedUser := r.Group("/api")
	authorizedUser.Use(auth.AuthMiddleware(authHandler.LogtoService, catToiletService))
	{
		// user auth info
		authorizedUser.GET("/user/info", authHandler.GetUserInfo)

		RegisterClientsRoutes(authorizedUser, catHandler)
		RegisterCatsAuthRoutes(authorizedUser, catHandler)
		RegisterUserAuthRoutes(authorizedUser, catHandler)
		RegisterFamilyGroupsAuthRoutes(authorizedUser, catHandler)
		RegisterNotificationsAuthRoutes(authorizedUser, notificationHandler)
		RegisterMetricsAuthRoutes(authorizedUser, catHandler)
		RegisterStorageRoutes(authorizedUser, catHandler)
	}

	// service to service routes
	authorizedService := r.Group("/api")
	authorizedService.Use(auth.ServiceTokenAuth(cfg))
	{
		RegisterRecordsServiceRoutes(authorizedService, catHandler)
	}

	return r
}
