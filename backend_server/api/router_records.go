package api

import (
	"cabycare-server/pkg/cattoilet"
	"cabycare-server/pkg/video"

	"github.com/gin-gonic/gin"
)

func RegisterRecordsRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler, videoHandler *video.Handler) {
	records := routerGroup.Group("/records")
	{
		records.POST("", catHandler.CreateShitRecord)
		records.GET("", catHandler.ListRecords)
		records.GET("/device/:device_id", catHandler.ListDeviceRecords)
		records.GET("/cat/:cat_id", catHandler.ListCatRecords)

		videos := records.Group("/videos")
		{
			videos.GET("/list", videoHandler.ListVideos)
			videos.GET("/get", videoHandler.GetPlaylist)
			videos.GET("/:folder/:filename", videoHandler.GetSegment)
			videos.GET("/thumbnail/:folder", videoHandler.GetThumbnail)
		}
	}
}

func RegisterRecordsServiceRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler) {
	records := routerGroup.Group("/records")
	{
		videoGroup := records.Group("/videos")
		{
			videoGroup.POST("/static/:video_id", catHandler.CheckVideoStatic)
		}
	}
}
