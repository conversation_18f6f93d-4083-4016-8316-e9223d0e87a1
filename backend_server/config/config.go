package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type Config struct {
	// MinIO配置
	Minio struct {
		Endpoint  string `yaml:"endpoint"`
		AccessKey string `yaml:"access_key"`
		SecretKey string `yaml:"secret_key"`
		UseSSL    bool   `yaml:"use_ssl"`
	} `yaml:"minio"`

	// MinIO OTA配置
	MinioOTA struct {
		Endpoint  string `yaml:"endpoint"`
		AccessKey string `yaml:"access_key"`
		SecretKey string `yaml:"secret_key"`
		UseSSL    bool   `yaml:"use_ssl"`
	} `yaml:"minio_ota"`

	// MySQL配置
	MySQL struct {
		Host     string `yaml:"host"`
		Port     int    `yaml:"port"`
		User     string `yaml:"user"`
		Password string `yaml:"password"`
		Database string `yaml:"database"`
	} `yaml:"mysql"`

	// Logto配置
	Logto struct {
		Endpoint    string `yaml:"endpoint"`
		AppID       string `yaml:"app_id"`
		AppSecret   string `yaml:"app_secret"`
		APIResource string `yaml:"api_resource"`
		CallbackURI string `yaml:"callback_uri"`
	} `yaml:"logto"`

	// Apple Push Notification Service 配置
	APS struct {
		TeamID      string `yaml:"team_id"`
		KeyID       string `yaml:"key_id"`
		BundleID    string `yaml:"bundle_id"`
		AuthKeyPath string `yaml:"auth_key_path"`
	} `yaml:"aps"`

	DBConfig struct {
		Host     string
		Port     int
		User     string
		Password string
		DBName   string
	}

	// CabyBackend配置
	CabyBackend struct {
		URL string `yaml:"url"`
	} `yaml:"caby_backend"`

	// CabyAI配置
	CabyAI struct {
		URL       string `yaml:"url"`
		AuthToken string `yaml:"auth_token"`
		MaxRetries int    `yaml:"max_retries"`
		TimeoutMinutes int    `yaml:"timeout_minutes"`
	} `yaml:"caby_ai"`

	// Device configuration
	DeviceConfig struct {
		HeartbeatInterval int `yaml:"heartbeat_interval"` // Heartbeat interval in minutes
		StatusTimeout     int `yaml:"status_timeout"`     // Time in minutes after which a device is considered offline
		StatisticsWindow  int `yaml:"statistics_window"`  // Window in hours for collecting statistics
	} `yaml:"device_config"`
}

func LoadConfig() (*Config, error) {
	cfg := &Config{}

	// 尝试从config.yaml读取配置
	configPath := "config/config.yaml"
	// 检查环境变量中是否指定了配置文件路径
	if envPath := os.Getenv("CONFIG_PATH"); envPath != "" {
		configPath = envPath
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("cannot read config file: %v", err)
	}

	// 解析配置
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("cannot parse config file: %v", err)
	}

	// 验证必要的配置是否存在
	if cfg.Minio.Endpoint == "" || cfg.Minio.AccessKey == "" || cfg.Minio.SecretKey == "" {
		return nil, fmt.Errorf("missing required MinIO configuration")
	}

	if cfg.MinioOTA.Endpoint == "" || cfg.MinioOTA.AccessKey == "" || cfg.MinioOTA.SecretKey == "" {
		return nil, fmt.Errorf("missing required MinIO OTA configuration")
	}

	if cfg.MySQL.Host == "" || cfg.MySQL.User == "" || cfg.MySQL.Database == "" {
		return nil, fmt.Errorf("missing required MySQL configuration")
	}

	// 验证 Logto 配置
	if cfg.Logto.Endpoint == "" || cfg.Logto.AppID == "" ||
	   cfg.Logto.AppSecret == "" || cfg.Logto.APIResource == "" ||
	  cfg.Logto.CallbackURI == "" {
		return nil, fmt.Errorf("missing required Logto configuration")
	}

	// 将 MySQL 配置复制到 DBConfig
	cfg.DBConfig = struct {
		Host     string
		Port     int
		User     string
		Password string
		DBName   string
	}{
		Host:     cfg.MySQL.Host,
		Port:     cfg.MySQL.Port,
		User:     cfg.MySQL.User,
		Password: cfg.MySQL.Password,
		DBName:   cfg.MySQL.Database,
	}

	return cfg, nil
}

// GetDB 获取数据库连接
func (c *Config) GetDB() (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		c.DBConfig.User,
		c.DBConfig.Password,
		c.DBConfig.Host,
		c.DBConfig.Port,
		c.DBConfig.DBName,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	// 测试连接
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %v", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	return db, nil
}
