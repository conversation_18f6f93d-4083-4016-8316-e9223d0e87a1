# 工厂永久Token使用指南

## 概述

为了方便工厂生产线和测试环境使用SN号管理API，我们提供了一个永久有效的Token认证机制。

## 🔑 您的永久工厂Token

```
FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K
```

**注意：请妥善保管此Token，不要泄露给未授权人员。**

## 📝 使用方法

### HTTP请求头格式

在所有API请求中，需要在HTTP请求头中添加以下认证信息：

```
Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K
```

### cURL示例

```bash
curl -X POST "https://api.caby.care/api/factory/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K" \
  -d '{
    "device_type": "toilet_v1",
    "production_line": "line_001",
    "batch_number": "batch_20250715_001",
    "operator": "张三",
    "quantity": 10,
    "remark": "生产批次"
  }'
```

### JavaScript示例

```javascript
const response = await fetch('https://api.caby.care/api/factory/sn/apply', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K'
  },
  body: JSON.stringify({
    device_type: 'toilet_v1',
    production_line: 'line_001',
    batch_number: 'batch_20250715_001',
    operator: '张三',
    quantity: 10,
    remark: '生产批次'
  })
});

const data = await response.json();
console.log(data);
```

### Python示例

```python
import requests

url = "https://api.caby.care/api/factory/sn/apply"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
}
data = {
    "device_type": "toilet_v1",
    "production_line": "line_001",
    "batch_number": "batch_20250715_001",
    "operator": "张三",
    "quantity": 10,
    "remark": "生产批次"
}

response = requests.post(url, json=data, headers=headers)
print(response.json())
```

## 🚀 可用的API端点

### 1. 申请SN号
- **URL**: `POST /api/factory/sn/apply`
- **用途**: 工厂生产线申请新的SN号
- **需要Token**: ✅

### 2. 检查SN是否存在
- **URL**: `GET /api/factory/sn/{sn}/check`
- **用途**: 检查指定SN号是否已存在
- **需要Token**: ✅

### 3. 获取SN详细信息
- **URL**: `GET /api/factory/sn/{sn}`
- **用途**: 获取SN号的详细信息
- **需要Token**: ✅

### 4. 更新SN状态
- **URL**: `PUT /api/factory/sn/update`
- **用途**: 更新SN号状态（已使用/已废弃）
- **需要Token**: ✅

### 5. 查询SN号
- **URL**: `GET /api/factory/sn/query`
- **用途**: 根据条件查询SN号记录
- **需要Token**: ✅

### 6. 获取统计信息
- **URL**: `GET /api/factory/statistics`
- **用途**: 获取SN号使用统计信息
- **需要Token**: ✅

### 7. 健康检查
- **URL**: `GET /api/factory/health`
- **用途**: 检查工厂服务状态
- **需要Token**: ❌（公开接口）

## 🔒 安全注意事项

1. **Token保密**: 请将Token存储在安全的地方，不要在代码中硬编码
2. **HTTPS**: 生产环境请务必使用HTTPS协议
3. **访问控制**: 只在需要的系统中使用此Token
4. **监控**: 建议监控Token的使用情况，发现异常及时联系管理员

## 🧪 测试脚本

我们提供了测试脚本来验证Token功能：

```bash
# 运行测试脚本
./scripts/test_factory_token.sh

# 或指定服务器地址
./scripts/test_factory_token.sh https://api.caby.care
```

## ❌ 常见错误

### 1. 401 Unauthorized - Authorization header required
**原因**: 请求中缺少Authorization头
**解决**: 添加正确的Authorization头

### 2. 401 Unauthorized - Invalid authorization format
**原因**: Token格式错误，缺少"Bearer "前缀
**解决**: 确保格式为 `Bearer TOKEN`

### 3. 401 Unauthorized - Invalid factory token
**原因**: Token值错误
**解决**: 检查Token是否正确复制

## 📞 技术支持

如果在使用过程中遇到问题，请联系技术支持团队。

---

**Token有效期**: 永久有效
**最后更新**: 2025-07-15
